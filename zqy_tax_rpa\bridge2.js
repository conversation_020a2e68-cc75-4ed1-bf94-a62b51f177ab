const Untils = require("./untils");
const untils = new Untils();
// let jiangsu = require('./jiangsuApi.js')
// let hubei = require('./hubei.js')
// let shanxi = require('./shanxi.js')
// let zhejiang = require('./zhejiang.js')
// let shaanxi = require('./shaanxi.js')
// let neimenggu = require('./neimenggu.js')
// let sichuan = require('./sichuan.js')
// let shanghai = require('./shanghai.js')
// let fujian = require('./fujian.js')
// let guangxi = require('./guangxi.js')
// let hebei = require('./hebei.js')
// let shandong = require('./shandong.js')
// let anhui = require('./anhui.js')
// let guangdong = require('./guangdong.js')
// let qinghai = require('./qinghai.js')
// let hunan = require('./hunan.js')
// let chongqing = require('./chongqing.js')
// let xinjiang = require('./xinjiang.js')
// let shenzhen = require('./shenzhen.js')
// let henan = require('./henan.js')
// let hainan = require('./hainan.js')
// let ningbo = require('./ningbo.js')
// let tianjin = require('./tianjin.js')
// let beijing = require('./beijing.js')
// let heilongjiang = require('./heilongjiang.js')
// let qingdao = require('./qingdao.js')
// let jiangxi = require('./jiangxi.js')
// let xiamen = require('./xiamen.js')
// let liaoning = require('./liaoning.js')
// let gansu = require('./gansu.js')
// let jilin = require('./jilin.js')
// let guizhou = require('./guizhou.js')

let readyToRPAToLogin = async (proItem, pbottleRPA, childrenTask) => {
  console.log("正在执行登录的省份：", proItem.url);
  //method 自定义方法名
  let provinceUrl;
  switch (proItem.url) {
    case "jiangsu":
      provinceUrl = `https://tpass.jiangsu.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.jiangsu.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=mcsc7e2ssscb4sfmbsmas35sass2753b&response_type=code&state=aea49396febf447fbc6164d6ad5f2dc4&client_pid=mcsc7e2ssscb4sfmbsmas35sass2753b`;
      break;
    case "hubei":
      provinceUrl = `https://tpass.hubei.chinatax.gov.cn:8443/#/login?redirect_uri=https://etax.hubei.chinatax.gov.cn/portal/login-web/api/third/sso/login/redirect&client_id=y852fe37d8e24d46ae8923fcd5yee6ac&response_type=code&state=test`;
      break;
    case "shanxi":
      provinceUrl =
        "https://tpass.shanxi.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.shanxi.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=sa67ddd29sc94cga9f96s7da2772s6d7&response_type=code&state=6cdb37064e924bddac89bc517fd359ae&client_pid=sa67ddd29sc94cga9f96s7da2772s6d7";
      break;
    case "zhejiang":
      provinceUrl = `https://tpass.zhejiang.chinatax.gov.cn:8443/#/login?redirect_uri=https://etax.zhejiang.chinatax.gov.cn/zjgfdzswj/main/kx/skip.html?service=https://etax.zhejiang.chinatax.gov.cn/zjgfdzswj/main/home/<USER>/index.html&client_id=xdf6a5cd4cx74bfca46x3bbfcxa4c4xf&response_type=code&state=test`;
      break;
    case "shaanxi":
      provinceUrl =
        "https://tpass.shaanxi.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.shaanxi.chinatax.gov.cn%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=gd4bbma9mf6a4559a372da5f5g8b4eb4&response_type=code&state=a0ec74e0a2774ff6bccf3aa7f9b03092&client_pid=gd4bbma9mf6a4559a372da5f5g8b4eb4";
      break;
    case "neimenggu":
      provinceUrl = `https://tpass.neimenggu.chinatax.gov.cn:8443/api/v1.0/auth/oauth2/login?redirect_uri=https://etax.neimenggu.chinatax.gov.cn/sword?ctrl=DzswjKxtxCtrl_getKxtxOauth2Token&client_id=ad90f54071ae5fb28ade2e3af8f2efc9&response_type=code&state=test`;
      break;
    case "sichuan":
      provinceUrl =
        "https://tpass.sichuan.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.sichuan.chinatax.gov.cn%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=dddkead26f29477b9ea237d6845b8f44&response_type=code&state=bd48d8beabf94624a71a290576121ca1&client_pid=7ede9cef518555089c0e890ae17e5ce1";
      break;
    case "shanghai":
      provinceUrl = `https://tpass.shanghai.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.shanghai.chinatax.gov.cn%2Flogin-web%2Fapi%2Fthird%2Fsso%2Flogin%2Fredirect%3Fqd%3DKEXIN%26channelId%3Dweb%26goto%3D30010666&client_id=d598efbeddc7558c98fc32197114a36b&response_type=code&state=pro`;
      break;
    case "fujian":
      provinceUrl =
        "https://tpass.fujian.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.fujian.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=y25c624344444baf8b5956934uac2523&response_type=code&state=ea9cc09bf14b438d87bdc0c7c1dd721a";
      break;
    case "guangxi":
      provinceUrl =
        "https://tpass.guangxi.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.guangxi.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=u96fafdfbhda4s699a89es4fsc2hua2e&response_type=code&state=c5fbfd5da46649a78f9b5d474f0ac105";
      break;
    case "hebei":
      provinceUrl =
        "https://tpass.hebei.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.hebei.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=ap5bfdavp66a49979apa5848fpaa5353&response_type=code&state=4fbe55b343084dd5bc4386a76bf51747&client_pid=ap5bfdavp66a49979apa5848fpaa5353";
      break;
    case "shandong":
      provinceUrl = `https://tpass.shandong.chinatax.gov.cn:8443/api/v1.0/auth/oauth2/login?redirect_uri=https%3A%2F%2Fetax.shandong.chinatax.gov.cn%2FKxsfrzAction.do%3Factivity%3Dlogin&client_id=s4ce78569sbs47w3b8bw38D56a3D5cc2&response_type=code&state=test`;
      break;
    case "anhui":
      provinceUrl =
        "https://tpass.anhui.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.anhui.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=n7b7ecp5696hna6f969b22pacch966nn&response_type=code&state=57a0dd6168e94277a67ed5f603ccb093&client_pid=n7b7ecp5696hna6f969b22pacch966nn";
      break;
    case "guangdong":
      provinceUrl =
        "https://tpass.guangdong.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.guangdong.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=c5djdncfa7nj4n2cajna2j68cndbj2fc&response_type=code&state=cbb6016f0f5d447685f7ac771a2595e9";
      break;
    case "qinghai":
      provinceUrl =
        "https://tpass.qinghai.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.qinghai.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=u6ret4ft7b5d4e55b5358rbtt3tu44a7&response_type=code&state=5d787901c3d44fa09f0d98e40403b176&client_pid=u6ret4ft7b5d4e55b5358rbtt3tu44a7";
      break;
    case "hunan":
      provinceUrl =
        "https://tpass.hunan.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.hunan.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=ue7c9954acea492784ac6g78939gc2e9&response_type=code&state=d728cc66851044a092393888feb6380d&client_pid=ue7c9954acea492784ac6g78939gc2e9";
      break;
    case "chongqing":
      provinceUrl =
        "https://tpass.chongqing.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.chongqing.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=y75ey676f5e445f6bs4f5b78d45bs2b6&response_type=code&state=fcf49959cfd243baa7f6f9a7d19534a2&client_pid=y75ey676f5e445f6bs4f5b78d45bs2b6";
      break;
    case "xinjiang":
      provinceUrl =
        "https://tpass.xinjiang.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.xinjiang.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=hc62e89d4ece439k8bb6f9f8k7a94hff&response_type=code&state=4bd93d7761fe43eeac66179986fd523b&client_pid=hc62e89d4ece439k8bb6f9f8k7a94hff";
      break;
    case "shenzhen":
      provinceUrl =
        "https://tpass.shenzhen.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.shenzhen.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=b5492bba3b4e4f37b977c354a2b26a59&response_type=code&state=290cb5c98f33494c8919fb63529554e4&client_pid=b5492bba3b4e4f37b977c354a2b26a59";
      break;
    case "henan":
      provinceUrl = `https://tpass.henan.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.henan.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=te95ftefe59243cbafta4fb82bbtfcte&response_type=code&state=bdac7b17248a4b619667cbed3844b819&client_pid=te95ftefe59243cbafta4fb82bbtfcte`;
      break;
    case "hainan":
      provinceUrl =
        "https://tpass.hainan.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.hainan.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=c73sas6335e74c3cc2cs645bs9797633&response_type=code&state=9c9e14a333614d408dc542aca0e79a2d&client_pid=c73sas6335e74c3cc2cs645bs9797633";
      break;
    case "ningbo":
      provinceUrl = `https://tpass.ningbo.chinatax.gov.cn:8443/#/login?redirect_uri=https://etax.ningbo.chinatax.gov.cn/login-web/api/third/sso/login/redirect?qd=kexin&channelId=web&goto=30010666&client_id=wwba3777ce294989b8226ccwb28w6a3d&response_type=code&state=prod`;
      break;
    case "tianjin":
      provinceUrl = `https://tpass.tianjin.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.tianjin.chinatax.gov.cn%2Foutsider%2Fsso%2FmainPage.do&client_id=x3ef652ax3294xz6beefnf5zadbf66cc&response_type=code&state=test`;
      break;
    case "beijing":
      provinceUrl =
        "https://tpass.beijing.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.beijing.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=se8dc9bba4s9466a93bb2qaab2b8c9ca&response_type=code&state=df1d6cdad5a249d69c5562b493256b49&client_pid=se8dc9bba4s9466a93bb2qaab2b8c9ca";
      break;
    case "heilongjiang":
      provinceUrl =
        "https://tpass.heilongjiang.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.heilongjiang.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=j36c2335ff6948fya33ffjaf49p5c9d4&response_type=code&state=eee09b9d448e4a8f9cebfe8c964d6bf9";
      break;
    case "qingdao":
      provinceUrl =
        "https://tpass.qingdao.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.qingdao.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=vt8337t23tv94cdvbdb875e7t8a357e2&response_type=code&state=b21bc79b3d7f42c089a7303cd4663751";
      break;
    case "jiangxi":
      provinceUrl =
        "https://tpass.jiangxi.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.jiangxi.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=h6h824d7df2d4d66bdb3ddbd42d43h8f&response_type=code&state=78fd757a5d2543479009fc1e06cb1e1e&client_pid=h6h824d7df2d4d66bdb3ddbd42d43h8f";
      break;
    case "xiamen":
      "https://tpass.xiamen.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.xiamen.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=y56b7aay5brf48f8aa7bf24dd54d775r&response_type=code&state=64ab1895f3cd4944830cdfee809c8b06";
      break;
    case "liaoning":
      provinceUrl =
        "https://tpass.liaoning.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.liaoning.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=yd7d85y3ac9a4adhb5387b3b7b7h892a&response_type=code&state=1553bd1f6cce459aa26a195f58cc2594&client_pid=yd7d85y3ac9a4adhb5387b3b7b7h892a";
      break;
    case "gansu":
      provinceUrl =
        "https://tpass.gansu.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.gansu.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=n4553b866na9459d9f24naesb5ds5asa&response_type=code&state=b668ce944f2b454c950bf14c0e8d70eb&client_pid=n4553b866na9459d9f24naesb5ds5asa";
      break;
    case "jilin":
      provinceUrl =
        "https://tpass.jilin.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.jilin.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=weadc9f99bf545dabaf46dwc6b249fp5&response_type=code&state=106a6eff6ba54de6900952a9b0d4735a";
      break;
    case "guizhou":
      provinceUrl = `https://tpass.guizhou.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.guizhou.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=aec7eia2ab294w26aw2899f9ded277e5&response_type=code&state=9e79228cfbd14aecb02e24e88659bec7`;
      break;
    default:
      throw new Error(`未知省份: ${proItem.province}`);
  }
  const rpa_to_login = async () => {
    return new Promise(async (resolve, reject) => {
      try {
        pbottleRPA.keyTap("Ctrl + Shift + W");
        let url = provinceUrl;
        // console.log('url11111',url)
        pbottleRPA.openURL(url);
        pbottleRPA.sleep(1000);
        // untils.closeOtherPages()
        pbottleRPA.keyTap("alt+space");
        pbottleRPA.keyTap("x"); // 浏览器最大化
        pbottleRPA.sleep(1000);
        let loginBTRes = await untils.existImage2("/input/1920/loginBT.png");
        console.log("loginBTRes:", loginBTRes);
        if (loginBTRes) {
          console.log("完成RPA打开税局页面节点");
          // console.log('调用addLog前的global.traceId:', global.traceId)
          untils.addLog(global.traceId, "RPA成功打开税局页面", "rpa_login");
          resolve(true)
        } else {
          console.log("获取税局页面失败，重新加载页面");
          untils.addLog(
            global.traceId,
            "获取税局页面失败，重新加载页面",
            "rpa_login"
          );
          return await readyToRPAToLogin(proItem, pbottleRPA, childrenTask);
        }
      } catch (error) {
        reject(false);
      }
    });
  };

  await rpa_to_login();
};

let getCookie = (proItem) => {
  console.log("正在执行登录的省份：", proItem.url);
  let cookie1 = "";
  let cookie2 = "";
  switch (proItem.url) {
    case "jiangsu":
      cookie1 = "tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58";
      cookie2 = "tpass_mcsc7e2ssscb4sfmbsmas35sass2753b";
      break;
    case "hubei":
      cookie1 = "tpass_n6s4de5cb89s4cf28997482cfnb7s4en";
      cookie2 = "tpass_b6edq5qqc5cb4c3288q6aq6fab5qc3b4";
      break;
    case "shanxi":
      cookie1 = "tpass_yb8ee73y2ehh445a9bhb39cf473e585a";
      cookie2 = "tpass_sa67ddd29sc94cga9f96s7da2772s6d7";
      break;
    case "zhejiang":
      cookie1 = "tpass_p3d8j63p8b6p4bbab8dca4832a7fj9cc";
      cookie2 = "tpass_tct8zta97w6c46zdt9zc2648227df5z2";
      break;
    case "shaanxi":
      break;
    case "neimenggu":
      cookie1 = "tpass_bx74f66a87d64bdf8ff8b7b9a4dfx375";
      cookie2 = "tpass_v93v3f7ed6af4vnan8nf2vn7n2vne93a";
      break;
    case "sichuan":
      break;
    case "shanghai":
      cookie1 = "tpass_ze998d5f7k8646c2af8c7k878e5ze7fd";
      cookie2 = "tpass_h49fdcfd823b49a6aasd3ffacb33bffh";
      break;
    case "fujian":
      cookie1 = "tpass_y25c624344444baf8b5956934uac2523";
      cookie2 = "tpass_y29fd969ada44b9fb2y9et5567ed262t";
      break;
    case "guangxi":
      cookie1 = "tpass_r36cb7e87rec486b9ffb5874br9eba2a";
      cookie2 = "tpass_u96fafdfbhda4s699a89es4fsc2hua2e";
      break;
    case "hebei":
      cookie1 = "tpass_h49fdcfd823b49a6aasd3ffacb33bffh";
      cookie2 = "tpass_ap5bfdavp66a49979apa5848fpaa5353";
      break;
    case "shandong":
      break;
    case "anhui":
      cookie1 = "tpass_vw5vafc9w7dd4e9e9w8d5vbc66889d55";
      cookie2 = "tpass_n7b7ecp5696hna6f969b22pacch966nn";
      break;
    case "guangdong":
      cookie1 = "tpass_ssdbs2iqe46q4fs6bc5cbif27ibe8ieb";
      cookie2 = "tpass_c5djdncfa7nj4n2cajna2j68cndbj2fc";
      break;
    case "qinghai":
      cookie1 = "tpass_acdc8d4qeq5c438eaac82q946454369q";
      cookie2 = "tpass_u6ret4ft7b5d4e55b5358rbtt3tu44a7";
      break;
    case "hunan":
      cookie1 = "tpass_kc98bk95c58k46g4adfbg5bkb5fd3659";
      cookie2 = "tpass_b5492bba3b4e4f37b977c354a2b26a59";
      break;
    case "chongqing":
      cookie1 = "tpass_sb2cb3a752324a5aaf78aeb5f5ebssac";
      cookie2 = "tpass_y75ey676f5e445f6bs4f5b78d45bs2b6";
      break;
    case "xinjiang":
      cookie1 = "tpass_ec95c8cwa7ef49ewad282efewe4229ea";
      cookie2 = "tpass_hc62e89d4ece439k8bb6f9f8k7a94hff";
      break;
    case "shenzhen":
      cookie1 = "tpass_b5492bba3b4e4f37b977c354a2b26a59";
      cookie2 = "tpass_kc98bk95c58k46g4adfbg5bkb5fd3659";
      break;
    case "henan":
      cookie1 = "tpass_s84ad3c7cecc4acdbcdc5e87cb37c7sc";
      cookie2 = "tpass_te95ftefe59243cbafta4fb82bbtfcte";
      break;
    case "hainan":
      cookie1 = "tpass_c84fc6b277aa476c92c2cc6cancbbf2c";
      cookie2 = "tpass_c73sas6335e74c3cc2cs645bs9797633";
      break;
    case "ningbo":
      break;
    case "tianjin":
      cookie1 = "tpass_mb29dc78543X4X769Hd7d8a35Hdd5236";
      cookie2 = "tpass_q4q6b8aa469v4369ae7483c4vb45cvca";
      break;
    case "beijing":
      cookie1 = "tpass_jcdjcb7ezjc84dz7az5fz4bb23cc3zc9";
      cookie2 = "tpass_n57nnace9fbnna67a92x5f7dn2n36c39";
      break;
    case "heilongjiang":
      cookie1 = "tpass_sse3d26fp9x94cxebc7c4674sdx25cef";
      cookie2 = "tpass_j36c2335ff6948fya33ffjaf49p5c9d4";
      break;
    case "qingdao":
      cookie1 = "tpass_vt8337t23tv94cdvbdb875e7t8a357e2";
      cookie2 = "tpass_f5d843e943fd4f7692ff953f35f7fccd";
      break;
    case "jiangxi":
      cookie1 = "tpass_e62p7bde6272429sp56pps44ppceps9p";
      cookie2 = "tpass_h6h824d7df2d4d66bdb3ddbd42d43h8f";
      break;
    case "xiamen":
      cookie1 = "tpass_udu889aab99b48f98cdccb36eeub995d";
      cookie2 = "tpass_y56b7aay5brf48f8aa7bf24dd54d775r";
      break;
    case "liaoning":
      cookie1 = "tpass_x7v6v686a2f44x99axaa44b2a72a6bea";
      cookie2 = "tpass_yd7d85y3ac9a4adhb5387b3b7b7h892a";
      break;
    case "gansu":
      cookie1 = "tpass_p9bgggp42aa744wabgg5ggg2367g2g96";
      cookie2 = "tpass_n4553b866na9459d9f24naesb5ds5asa";
      break;
    case "jilin":
      cookie1 = "tpass_g6x9dxdrx94847xr9556584ab65rg5bb";
      cookie2 = "tpass_weadc9f99bf545dabaf46dwc6b249fp5";
      break;
    case "guizhou":
      break;
    default:
      throw new Error(`未知省份: ${proItem.province}`);
  }

  return {
    cookie1: cookie1,
    cookie2: cookie2,
  };
};

module.exports = {
  readyToRPAToLogin,
  getCookie,
};
