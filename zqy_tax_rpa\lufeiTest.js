const { getherInvoice, downloadEXCEL, downloadPDF, getPDF } = require('./Download.js')
const pbottleRPA = require("./pbottleRPA");
const { spawn } = require('child_process');

main()

async function main(){
	// const begin = '' 
    // const end = ''
	const begin = 1573574400000 
    const end = 1762876800000
	const times = calculateYear(begin,end)
 	if(begin && end){
            currentDate = new Date(end) 
	}else{
		currentDate = new Date();
			// 获取当前日期的上一个月
		currentDate.setMonth(currentDate.getMonth() - 1);
	}
	for (var i = 1; i <= times; i++) {
		if (begin && end) {
			if( i == 1 ){
				var startDate = new Date(
					currentDate.getFullYear()-1,
					currentDate.getMonth(),
					currentDate.getDate() + 1 
				)
				var endDate = new Date(end)
			}else{
				var startDate = new Date(
					currentDate.getFullYear()-1,
					currentDate.getMonth(),
					currentDate.getDate() + 1 
				)
				var endDate = new Date(
					currentDate.getFullYear(),
					currentDate.getMonth() ,
					currentDate.getDate()
				);
			}
			if( i == times ){
				var startDate = new Date(begin)
			}else{
				var endDate = new Date(
					currentDate.getFullYear(),
					currentDate.getMonth() ,
					currentDate.getDate()
				);
			}
	
		}else{
			// 获取11个月前的日期
			var startDate = new Date(
				currentDate.getFullYear(),
				currentDate.getMonth() - 11,
				1
			);
			// 获取当前月份的最后一天
			var endDate = new Date(
				currentDate.getFullYear(),
				currentDate.getMonth() + 1,
				0
			);
		}
		
		// 输出日期范围
		console.log(formatDate(startDate) + "---------->" + formatDate(endDate));

		// 将当前日期往前推12个月
		currentDate.setMonth(currentDate.getMonth() - 12);
	}
}

function calculateYear (startTime, endTime) {
    if(startTime,endTime){
         // 将时间戳转换为Date对象
        const startDate = new Date(startTime);
        const endDate = new Date(endTime);
        // 获取年份
        const starYear = startDate.getFullYear();
        const endYear = endDate.getFullYear();
        // 计算年份差
        let yearDifference = endYear - starYear;
        // 检查是否跨年但不足一年的情况
        if (endDate.getMonth() < startDate.getMonth() || (endDate.getMonth() === startDate.getMonth() && endDate.getDate() < startDate.getDate())) {
            yearDifference--;
        }
        // 年份差加一
        yearDifference++;
        console.log('年份差（加一后）:', yearDifference);
        return yearDifference;
    }else{
        return 3
	}
}


function formatDate (date) {
    var year = date.getFullYear();
    var month = padZero(date.getMonth() + 1);
    var day = padZero(date.getDate());
    return year + "-" + month + "-" + day;
}

function padZero (num) {
    return num < 10 ? "0" + num : "" + num;
}