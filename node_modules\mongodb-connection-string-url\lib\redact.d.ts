import ConnectionString from './index';
export interface ConnectionStringRedactionOptions {
    redactUsernames?: boolean;
    replacementString?: string;
}
export declare function redactValidConnectionString(inputUrl: Readonly<ConnectionString>, options?: ConnectionStringRedactionOptions): ConnectionString;
export declare function redactConnectionString(uri: string, options?: ConnectionStringRedactionOptions): string;
