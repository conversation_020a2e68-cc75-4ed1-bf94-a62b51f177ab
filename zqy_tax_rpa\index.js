const pbottleRPA = require('./pbottleRPA')
const xlsx = require("node-xlsx");
const fs = require('fs')
const php = require("php2javascript");
const request = require('sync-request'); //默认同步请求
const {
	FormData
} = require('sync-request');
const compressing = require('compressing');

const { getProClassAndExtend } = require('./bridge.js');

const Untils = require('./untils')

//读取excel配置
const workSheetsFromFile = xlsx.parse(`${__dirname}\\配置项.xlsx`);
const config_sheet = workSheetsFromFile[0].data;
console.log(config_sheet);

let global_access_token = '';
//新增global_rpaname
let global_rpaname = config_sheet[1][0];
let global_download_path = config_sheet[1][2];
let global_apiServer = config_sheet[1][3];

// 新四个字段
let global_flowId = '';
let global_taskKey = '';
let global_requestId = '';
let global_dq = '';
let global_taskInfo = {}
let untils = new Untils();

async function process_prepare() {

	let resolution = pbottleRPA.getResolution()
	console.log('当前电脑屏幕分辨率', resolution)

	if (resolution.w != 1920) {
		pbottleRPA.tts('分辨率不适配，退出。')
		console.log("分辨率不适配，退出。");
		pbottleRPA.exit()
	}

	// 初始化 untils 对象
	let untils = new Untils();
	global.traceId = global_rpaname + untils.generateRandomString(14);
	
	// 老猫本地
	// json = await untils.http('GET','operate/getIdleTask',null,{
	// 	'rpaName':global_rpaname
	// },true)

	json = await untils.http('GET','flow/operate/getIdleTask',null,{
		'rpaName':global_rpaname
	},true)

	console.log('json----', json)

	if (!json.data) {
		pbottleRPA.sleep(1000 * 2)
		pbottleRPA.exit('当前任务列表为空，退出')
	}
	/*
	* lufei:节点ID改为flowId
	* */
	global_flowId = json.data.flowId;
	global_requestId = json.data.requestId
	global_taskKey = json.data.taskKey

	// console.log('processInstanceId: ', global_processInstanceId)
	console.log('global_requestId',global_requestId)
	console.log('global_taskKey',global_taskKey)
	console.log('global_flowId',global_flowId)
	untils.addLog(global.traceId,`任务开始，当前任务requestId：${global_requestId}，当前任务flowId：${global_flowId}`,'Task_Begin')
	//清理目录旧文件
	fs.readdirSync(global_download_path).forEach((file) => {
		fs.unlinkSync(`${global_download_path}/${file}`);
	})
	return json
}

async function childrenTaskExtend() {
	//领取子任务

	let json = await untils.getRpaTodoTask()

	let rs = untils.http('GET', `flow/operate/getFlowStatus`, null,{
		'flowId':global_flowId,
	}, true)
	console.log("当前流程完成情况", rs)
	// if(rs.code !== 500){
	// 	untils.addLog(global.traceId,`当前流程完成情况：${JSON.stringify(rs)}`,'getFlowStatus')
	// }
	// let json = JSON.parse(rs.getBody('utf8'))

	// 老猫本地
	// let rs = untils.http('GET', `operate/getFlowStatus`, null,{
	// 	'flowId':global_flowId,
	// }, true)

	if (rs.data.status !== 1) {
		// 子任务执行完毕，流程状态为完成或者终止时，return
		return
	}

	if(!json.data){
		pbottleRPA.sleep(1000)
		return await childrenTaskExtend()
	}

	console.log('获取待执行子任务', json.data)
	// console.log('子任务',json.data.records[0])
	let childrenRes = await getProClassAndExtend(global_dq, json.data, pbottleRPA)
	
	return await childrenTaskExtend()
}


async function main() {
	//获取当前账号认领的主任务，或者没认领的主任务
	let prepareRes = await process_prepare()
	// console.log('prepareRes.data.records[0]',prepareRes.data.records[0])

	if (prepareRes?.data) {

		//有主任务，抢主任务
		// let prepareItem = prepareRes.data
		// let getRpaTodoTask = await untils.getRpaTodoTask()

		if (prepareRes.state != 'success') {
			pbottleRPA.exit('任务领取失败！~')
		}
		global_taskInfo = prepareRes.data;
		console.log('global_taskInfo.variable',global_taskInfo.variable)
		
		global_dq  = JSON.parse(global_taskInfo.variable).dq
		console.log('global_dq',global_dq)

	} else {
		//没有主任务
		console.log("没有主任务")
		return
	}

	//调用子任务执行器
	console.log('进入childrenTaskExtend')
	await childrenTaskExtend()

	pbottleRPA.keyTap('Ctrl + Shift + W')
	pbottleRPA.keyTap('Ctrl + Shift + W')
	pbottleRPA.exit('当前子任务列表为空，退出')

}
process.on('uncaughtException', function(err) {
	pbottleRPA.keyTap('Ctrl + Shift + W');
	console.error('同步运行错误：', err);
});
process.on('unhandledRejection', function(reason, promise) {
	pbottleRPA.keyTap('Ctrl + Shift + W');
	console.error('异步运行错误：', promise, reason);
});

main();
