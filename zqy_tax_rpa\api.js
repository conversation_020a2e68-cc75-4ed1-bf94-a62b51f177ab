const pbottleRPA = require('./pbottleRPA')
const xlsx = require("node-xlsx");
const fs = require('fs')
const php = require("php2javascript");
const request = require('sync-request'); //默认同步请求
const {
	FormData
} = require('sync-request');
const compressing = require('compressing');

let currentTime = new Date();
let year = currentTime.getFullYear();
console.log(year)
pbottleRPA.sleep(500)


const api_url = `http://127.0.0.1:49888/?action=pbottleRPA_run&path="C:\Users\<USER>\Desktop\pbottleRPA\小瓶 RPA.exe"`; 

function call_api() {
    fetch(api_url)
        .then(response => response.json())
        .then(data => {
            // 在这里处理接口返回的数据
            console.log(data);
        })
        .catch(error => {
            console.error("Error calling API:", error);
        });
}

// 每隔5秒调用一次外部接口
setInterval(() => {
    try {
        call_api();
    } catch (error) {
        console.error("Caught an error:", error);
    }
}, 5000);



 check_code(childrenTask) {
    let that = this; // 将当前的 this 存储到 that 变量中

    let startTime = Date.now();
    let intervalId = setInterval(function() {
        let currentTime = Date.now();
        if (currentTime - startTime >= 5 * 60 * 1000) {
            this.untils.http('POST', '/operate/terminate', {
                'flowId': this.childrenTask.flowId,
            })
            this.pbottleRPA.exit('验证码未输入，终止任务')
            clearInterval(intervalId); // 时间到达5分钟后清除循环定时器
        } else {
            console.log("childrenTask.variables.code", childrenTask.variables.code)
           if (this.childrenTask.variables.code) {
           	// pbottleRPA.browserCMD_click(`input[placeholder="请输入短信验证码"] `)
           	let yanzhengma = this.pbottleRPA.findScreen("/input/1920/yanzhengma.png")
           	if (yanzhengma) {
           		this.pbottleRPA.moveAndClick(yanzhengma.x + 50, yanzhengma.y)
           	}
           	this.pbottleRPA.keyTap('ctrl+a')
           	this.pbottleRPA.paste(this.childrenTask.variables.code)
           	this.pbottleRPA.browserCMD_click(`button span:contains(登录)`)
           
           	//验证码判断
           	let codeerr = this.pbottleRPA.findScreen("/input/1920/codeerr.png")
           	console.log('codeerr', codeerr)
           	if (codeerr) {
           		//验证失败
           		// pbottleRPA.keyTap('alt+f4')
           		let rollBackRes = this.untils.http("POST", 'api/blade-workflow/app/task/rollbackTask', {
           			'taskId': this.childrenTask.taskId,
           			"nodeId": "commit_code",
           			"comment": '验证码错误',
           			"rejecter": "check_code" // 驳回者节点
           		}, null, false)
           		console.log('rollBackRes', rollBackRes)
           		return
           	} else {
           		//验证码验证成功
           		let res = this.untils.completeTask(this.childrenTask.taskId, 1, "验证成功")
           		console.log('完成结果', res)
           	}
           	clearInterval(intervalId); // 找到值A后清除循环定时器
            }
        }
    }, 1000);
}

