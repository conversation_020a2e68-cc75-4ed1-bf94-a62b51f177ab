const Untils = require('./untils')
const request = require('sync-request'); //默认同步请求
const pbottleRPA = require('./pbottleRPA')
const xlsx = require("node-xlsx");
const fs = require('fs')
const php = require("php2javascript");
const {
	FormData
} = require('sync-request');
const ExcelJS = require('exceljs');
const path = require('path');
const compressing = require('compressing');
const workSheetsFromFile = xlsx.parse(`${__dirname}\\配置项.xlsx`);
const config_sheet = workSheetsFromFile[0].data;
const global_download_path = config_sheet[1][2];
class ExtendClass {
	constructor(proItem, pbottleRPA, childrenTask, global_access_token) {

		this.proItem = proItem; // 将传入的值存储在类的属性中
		this.childrenTask = childrenTask; // 将传入的值存储在类的属性中
		this.pbottleRPA = pbottleRPA
		this.untils = new Untils(global_access_token)
	}

	rpa_to_login() {
		this.pbottleRPA.keyTap('Ctrl + Shift + W');
		let url = `https://etax.${this.proItem.url}.chinatax.gov.cn/sso/login`
		let res1 = request('GET', url);
		var regex = /var kxUrl = "([^"]+)"/;
		const matchResult = res1.body.toString().match(regex); // 将响应体转换为字符串
		if (matchResult && matchResult.length > 1) {
			const kxUrl = matchResult[1];
			console.log(kxUrl); // 输出提取到的 kxUrl
			this.pbottleRPA.openURL(kxUrl)
			this.pbottleRPA.keyTap('alt+space')
			this.pbottleRPA.keyTap('x') // 浏览器最大化
		} else {
			console.log('未找到 kxUrl');
		}

		let ready = `input[placeholder="统一社会信用代码/纳税人识别号"]`
		if (ready) {
			let res = this.untils.completeTask(this.childrenTask.taskId, 1, "进入登录页成功")
			console.log('完成RPA登录税局节点', res)
		} else {
			let res = this.untils.completeTask(this.childrenTask.taskId, 0, "进入登录页失败")
			console.log('完成RPA登录税局节点', res)
		}
		return
	}

	// 二维码登录进入二维码登录页
	rpa_to_codelogin() {
		this.pbottleRPA.keyTap('Ctrl + Shift + W');
		let url = `https://etax.${this.proItem.url}.chinatax.gov.cn/sso/login`
		let res1 = request('GET', url);
		var regex = /var kxUrl = "([^"]+)"/;
		const matchResult = res1.body.toString().match(regex); // 将响应体转换为字符串
		if (matchResult && matchResult.length > 1) {
			const kxUrl = matchResult[1];
			console.log(kxUrl); // 输出提取到的 kxUrl
			this.pbottleRPA.openURL(kxUrl)
			this.pbottleRPA.keyTap('alt+space')
			this.pbottleRPA.keyTap('x') // 浏览器最大化
		} else {
			console.log('未找到 kxUrl');
		}

		let ready = `input[placeholder="统一社会信用代码/纳税人识别号"]`
		if (ready) {
			let res = this.untils.completeTask(this.childrenTask.taskId, 1, "进入登录页成功")
			console.log('完成RPA登录税局节点', res)
		} else {
			let res = this.untils.completeTask(this.childrenTask.taskId, 0, "进入登录页失败")
			console.log('完成RPA登录税局节点', res)
		}
		return
	}

	// 选择登录身份
	select_sf() {
		// value=03：办税员   value=05 管理员  
		let select = this.untils.isImage("/input/1920/sflx.png", 5)
		if (select) {
			this.pbottleRPA.browserCMD_click(`div[aria-label="身份类型选择"] button span`)
			this.pbottleRPA.sleep(3000)
			let res = this.untils.completeTask(this.childrenTask.taskId, 1, "选择身份成功")
		} else {
			this.pbottleRPA.browserCMD_click(`div[aria-label="身份类型选择"] button span`)
			console.log('没找到该图片2')
			let res = this.untils.completeTask(this.childrenTask.taskId, 1, "无需选择身份")
		}
		return
	}

	// 检查登录身份权限
	async check_sf() {
		let sfinfo = "";
		for (let index = 0; index < 100; index++) {
			this.pbottleRPA.sleep(100);
			sfinfo = this.pbottleRPA.browserCMD_text('#div_user_info > div > div > div:nth-child(5) b');
			if (sfinfo != "ok") {
				break;
			}
		}
		console.log("sfinfo", sfinfo);
		let value = sfinfo.replace(/\s/g, "");

		if (value.includes("法定") || value.includes("财务") || value.includes("")) {
			console.log("当前登录人员有权限添加用户")
			let res = this.untils.completeTask(this.childrenTask.taskId, 1, "有权限添加用户")
		} else {
			let rollBackRes = this.untils.http("POST", 'api/blade-workflow/app/task/rollbackTask', {
				'taskId': this.childrenTask.taskId,
				"nodeId": "selectLogin",
				"comment": "该账号无权限添加用户，请更换法人账号或者财务管理员账号",
				"rejecter": "check_sf"
			}, null, false)
			console.log('rollBackRes', rollBackRes)
			return
		}
	}

	api_userpass() {
		this.pbottleRPA.keyTap('Ctrl + Shift + W');
		let url = `https://etax.${this.proItem.url}.chinatax.gov.cn/sso/login`
		let res = request('GET', url);
		console.log(res);
		var regex = /var kxUrl = "([^"]+)"/;
		const matchResult = res.body.toString().match(regex); // 将响应体转换为字符串
		if (matchResult && matchResult.length > 1) {
			const kxUrl = matchResult[1];
			console.log(kxUrl); // 输出提取到的 kxUrl
			this.pbottleRPA.openURL(kxUrl)
		} else {
			console.log('未找到 kxUrl');
		}

		let ready = this.untils.waitImage("/input/1920/loginready.png")
		this.pbottleRPA.moveMouseSmooth(ready.x, ready.y)
		this.pbottleRPA.mouseClick() //focus
		console.log("this.childrenTask.variables.nsrsbh", this.childrenTask.variables.nsrsbh)
		console.log("this.childrenTask.variables.telX", this.childrenTask.variables.telX)
		console.log("this.childrenTask.variables.bsymm", this.childrenTask.variables.bsymm)
		this.pbottleRPA.paste(this.childrenTask.variables.nsrsbh)
		this.pbottleRPA.keyTap('tab')
		this.pbottleRPA.paste(this.childrenTask.variables.telX)
		this.pbottleRPA.keyTap('tab')
		this.pbottleRPA.paste(this.childrenTask.variables.bsymm)
		this.pbottleRPA.keyTap('tab')

		this.pbottleRPA.moveMouse(ready.x - 153, ready.y + 180)
		this.pbottleRPA.mouseLeftDragTo(ready.x - 153 + 370, ready.y + 180)
		this.pbottleRPA.browserCMD_click(`button span:contains(登录)`)
		let value = this.untils.checkLogin()
		if (value.result == 0) {
			let res = this.untils.completeTask(this.childrenTask.taskId, value.result, value.message)
			console.log('获取接口参数输入账密节点', res)
		} else {
			let res = this.untils.completeTask(this.childrenTask.taskId, value.result, value.message)
			console.log('获取接口参数输入账密节点', res)
		}
		return {}
	}

	// async gather_invoice() {
	// 	console.log('进入gather_invoice')
	// 	this.pbottleRPA.openURL(
	// 		`https://dppt.${this.proItem.url}.chinatax.gov.cn:8443/invoice-query/invoice-query/`)

	// 	let chaxuntype = this.untils.waitImage("/input/1920/chaxuntype.png")
	// 	if (chaxuntype) {
	// 		this.pbottleRPA.moveMouseSmooth(chaxuntype.x, chaxuntype.y)
	// 	}

	// 	for (let index = 0; index < 2; index++) {
	// 		if (index == 1) {
	// 			//chaxuntype.png
	// 			this.pbottleRPA.moveMouseSmooth(chaxuntype.x + 150, chaxuntype.y)
	// 			this.pbottleRPA.mouseClick() //focus
	// 			this.pbottleRPA.browserCMD_click(`li span:contains(取得发票)`)
	// 		}

	// 		const fplx = (index === 0) ? '销项' : '进项';
	// 		// 获取当前日期
	// 		var currentDate = new Date();

	// 		// 获取当前日期的上一个月
	// 		currentDate.setMonth(currentDate.getMonth() - 1);

	// 		// 循环输出36个月的数据
	// 		for (var i = 1; i <= 3; i++) {
	// 			// 获取12个月前的日期
	// 			var startDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - 11, 1);

	// 			// 获取当前月份的最后一天
	// 			var endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

	// 			// 输出日期范围
	// 			console.log(formatDate(startDate) + '---------->' + formatDate(endDate));

	// 			// 将当前日期往前推12个月
	// 			currentDate.setMonth(currentDate.getMonth() - 12);

	// 			this.pbottleRPA.browserCMD_click(`input[placeholder="开票日期起"]`)
	// 			this.pbottleRPA.browserCMD_val('input[placeholder="开票日期起"]', formatDate(startDate));
	// 			this.pbottleRPA.keyTap('Enter')

	// 			this.pbottleRPA.browserCMD_click(`input[placeholder="开票日期止"]`)
	// 			this.pbottleRPA.browserCMD_val('input[placeholder="开票日期止"]', formatDate(endDate));
	// 			this.pbottleRPA.keyTap('Enter')

	// 			this.pbottleRPA.browserCMD_click(`button span:contains(查询)`)

	// 			for (var ins = 1; ins <= 50; ins++) {
	// 				this.pbottleRPA.sleep(300)
	// 				let text = this.pbottleRPA.browserCMD_text(
	// 					`div[class="t-pagination__total"]`) //div[class = "statistics-info"] span:nth-child(1)
	// 				let match = text.match(/\d+/);
	// 				let number = match ? parseInt(match[0]) : 0;
	// 				console.log("===============", ins, text, number)
	// 				if (parseInt(number) > 0 && parseInt(number) < 5000) {
	// 					console.log("-------------查询结束---------------")
	// 					this.pbottleRPA.browserCMD_click(`button span:contains(导出 )`)
	// 					this.pbottleRPA.browserCMD_click(`li span:contains(导出全部)`)
	// 					let down = this.untils.waitImage("/input/1920/dowloading.png")
	// 					if (down) {
	// 						console.log("-------------开始下载---------------")
	// 						break;
	// 					}
	// 					break;
	// 				} else if (parseInt(number) > 5000) {
	// 					// 发票数量大于5000，异步下载
	// 					console.log("-------------异步下载---------------")
	// 					this.pbottleRPA.sleep(1000)
	// 					this.pbottleRPA.browserCMD_click(`button span:contains(导出 )`)
	// 					this.pbottleRPA.browserCMD_click(`li span:contains(导出全部)`)

	// 					let wxts = this.untils.waitImage("/input/1920/wxts.png")
	// 					if (wxts) {
	// 						console.log("温馨提示")
	// 						this.pbottleRPA.moveMouseSmooth(wxts.x + 20, wxts.y + 170)
	// 						this.pbottleRPA.mouseClick() //focus
	// 					}

	// 					let guanbi = this.untils.waitImage("/input/1920/guanbi.png")
	// 					if (guanbi) {
	// 						console.log("关闭")
	// 						this.pbottleRPA.moveMouseSmooth(guanbi.x, guanbi.y)
	// 						this.pbottleRPA.mouseClick() //focus
	// 					}

	// 					this.pbottleRPA.openURL(
	// 						`https://dppt.${this.proItem.url}.chinatax.gov.cn:8443/importing-exporting-batches-search/`)
	// 					let chaxun = this.untils.waitImage("/input/1920/chaxun.png")
	// 					if (chaxun) {
	// 						this.pbottleRPA.browserCMD_click(`div[class="t-form__controls"] button[type="submit"]`)
	// 					}

	// 					const startTime = Date.now(); // 记录开始时间
	// 					while (true) {
	// 						this.pbottleRPA.sleep(30000)
	// 						this.pbottleRPA.browserCMD_click(`div[class="t-form__controls"] button[type="submit"]`)
	// 						let download = this.pbottleRPA.browserCMD_text(
	// 							`div[class="t-table__content"] tr:nth-child(1) span[class="operate"]`)
	// 						if (download == '下载') {
	// 							console.log("等待下载")
	// 							this.pbottleRPA.sleep(1000)
	// 							this.pbottleRPA.browserCMD_click(
	// 								`div[class="t-table__content"] tr:nth-child(1) span[class="operate"]`)
	// 							this.pbottleRPA.sleep(1000)
	// 							this.pbottleRPA.keyTap('Ctrl + W');
	// 							break;
	// 						}

	// 						const currentTime = Date.now();
	// 						const elapsedTime = Math.floor((currentTime - startTime) / 1000); // 计算已经过去的时间（秒）
	// 						if (elapsedTime >= 900) {
	// 							let res = this.untils.completeTask(this.childrenTask.taskId, 0, "采集发票失败")
	// 						}
	// 					}
	// 					break;
	// 				} else {
	// 					console.log("-------------查询未结束---------------")
	// 				}

	// 				let down1 = this.untils.waitImageDisappear("/input/1920/dowloading.png")
	// 				if (down1) {
	// 					console.log("-------------已下载---------------")
	// 					this.pbottleRPA.sleep(1000)

	// 					// let newFileName = `${formatDate(startDate)}` + '---' + `${formatDate(endDate)}` + '年度' +
	// 					// 	`${fplx}` + '数据' +
	// 					// 	renameDownloadedFile(newFileName)
	// 				}

	// 				if (ins == 50) {
	// 					console.log("-------------查询无发票数据--------------")
	// 					console.log(`${formatDate(startDate)}` + '---' + `${formatDate(endDate)}` + "年度无" +
	// 						`${fplx}` +
	// 						"数据")
	// 				}
	// 			}
	// 		}
	// 	}

	// 	// 设定日期格式
	// 	function formatDate(date) {
	// 		var year = date.getFullYear();
	// 		var month = padZero(date.getMonth() + 1);
	// 		var day = padZero(date.getDate());
	// 		return year + '-' + month + '-' + day;
	// 	}

	// 	// 给小于10的数字前面补0
	// 	function padZero(num) {
	// 		return num < 10 ? '0' + num : '' + num;
	// 	}

	// 	// 在每次下载完成后调用这个函数来对文件进行重命名
	// 	function renameDownloadedFile(newFileName) {
	// 		let latestDownloadedFilePath = 'C:/Users/<USER>/Downloads/RPAdownload/全量发票查询导出结果.xlsx';

	// 		let newFilePath = latestDownloadedFilePath.substring(0, latestDownloadedFilePath.lastIndexOf('/') + 1) +
	// 			newFileName;
	// 		console.log("新文件路径：", newFilePath);

	// 		fs.access(latestDownloadedFilePath, fs.constants.F_OK, (err) => {
	// 			if (err) {
	// 				console.error(`文件 ${latestDownloadedFilePath} 不存在，无法进行重命名操作。`);
	// 			} else {
	// 				fs.rename(latestDownloadedFilePath, newFilePath, (renameErr) => {
	// 					if (renameErr) {
	// 						console.error('重命名文件时出错：', renameErr);
	// 					} else {
	// 						console.log(`文件已成功重命名为：${newFileName}`);
	// 					}
	// 				});
	// 			}
	// 		});
	// 	}

	// 	this.untils.checkDownloadedFiles();

	// 	// 上传文件
	// 	let file = await this.untils.uploadFile(0)

	// 	let json = this.untils.http('POST', 'api/blade-workflow/app/task/rpaCompleteTask', {
	// 		'taskId': this.childrenTask.taskId,
	// 		"pass": true,
	// 		"variables": {
	// 			"invoiceFiles": [{
	// 				label: "发票文件打包",
	// 				value: file
	// 			}, ]
	// 		}
	// 	}, null, false)

	// 	if (!json.success) {
	// 		this.pbottleRPA.exit('子任务提交失败:' + global_taskName)
	// 	}
	// 	return
	// }

	gather_taxpayer() {
		this.pbottleRPA.openURL(`https://etax.${this.proItem.url}.chinatax.gov.cn:8443/loginb/`)

		let rs = this.untils.waitImage("/input/jiangsu/swszzh.png")
		this.pbottleRPA.moveAndClick(rs.x, rs.y)

		this.pbottleRPA.sleep(3000)
		const date = new Date();
		const timestamp = date.getTime();
		let url = `https://etax.${this.proItem.url}.chinatax.gov.cn:8443/szzh/zhcx/nsrxxcx?ruuid=${timestamp}`
		this.pbottleRPA.openURL(url)
		let zgxx = this.untils.waitImage("/input/jiangsu/zgxx.png")
		if (zgxx) {
			this.pbottleRPA.browserCMD_click('span:contains(资格信息查询)')
		} else {
			this.pbottleRPA.browserCMD_click('span:contains(资格信息查询)')
		}

		var page = this.pbottleRPA.browserCMD_text(
			'div[class="sfzrdxx"] div.t-table__pagination div.t-pagination__total')
		var numbers = page.match(/\d+/g);
		if (numbers) {
			numbers.forEach(number => console.log(number));
			console.log("page:", numbers)
		} else {
			console.log("未找到page")
		}

		var NSRZG = this.pbottleRPA.browserCMD_text(
			`div[class="sfzrdxx"] table  tbody tr:nth-child(${numbers}) td:nth-child(2)`)
		var RDRQ = this.pbottleRPA.browserCMD_text(
			`div[class="sfzrdxx"] table  tbody tr:nth-child(${numbers}) td:nth-child(6)`)
		var YXQ_Q = this.pbottleRPA.browserCMD_text(
			`div[class="sfzrdxx"] table  tbody tr:nth-child(${numbers}) td:nth-child(3)`)
		var YXQ_Z = this.pbottleRPA.browserCMD_text(
			`div[class="sfzrdxx"] table  tbody tr:nth-child(${numbers}) td:nth-child(4)`)

		console.log(NSRZG, RDRQ, YXQ_Q, YXQ_Z);
		this.pbottleRPA.keyTap('Ctrl + Shift + W');

		// 完成子任务
		let json = this.untils.http('POST', 'api/blade-workflow/app/task/rpaCompleteTask', {
			'taskId': this.childrenTask.taskId,
			"pass": true,
			"variables": {
				"taxpayerName": NSRZG,
				"taxpayerDate": RDRQ,
				"taxpayerStartDate": YXQ_Q,
				"taxpayerEndDate": YXQ_Z,
			}
		}, null, false)

		if (!json.success) {
			this.pbottleRPA.exit('子任务提交失败:' + global_taskName)
		}
		return
	}

	async gather_tax() {
		let url1 = await this.process_taxVat()
		let url2 = await this.process_taxIncome()
		let url3 = await this.process_financial()
		let url4 = await this.process_taxLevy()
		let url5 = await this.process_taxRatLevle()
		await this.process_submit(url1, url2, url3, url4, url5)
		return
	}

	/**
	 * 增值税
	 */
	async process_taxVat() {
		console.log("开始下载增值税")
		this.pbottleRPA.openURL(
			'https://etax.jiangsu.chinatax.gov.cn/shenbao/sbcxAction.action?sign=index&sb_type=1&zsxm_dm=10101&sbqx_bm=null'
		)

		this.pbottleRPA.browserCMD_val('select[name="zt"]', '1') // 默认为1 显示全部
		this.pbottleRPA.browserCMD_val('input[name="begin_skssq"]', php.date('Y-m-01', php.strtotime('-37 month')))
		this.pbottleRPA.browserCMD_val('input[name="sbrq"]', '')
		this.pbottleRPA.browserCMD_click(`div#query_btn`)
		this.pbottleRPA.sleep(1000 * 3);

		for (let index = 0; index < 99; index++) {

			this.pbottleRPA.browserCMD_click(`table.commontb tr:contains(增值税):first input[value="查看"]:first`)
			this.pbottleRPA.sleep(1000)

			let xiangqing = this.pbottleRPA.findScreen("/input/1920/xiangqing.png")
			if (xiangqing === false) {
				break;
			}

			let fromY = xiangqing.y + 30;
			for (let index2 = 0; index2 < 99; index2++) {
				this.pbottleRPA.sleep(500)
				let position = this.pbottleRPA.findScreen("/input/1920/xiazai.png", 0.8, 1000, fromY, 920, 40)
				if (position === false) {
					let page = this.untils.getCurrentWindowUrl()
					if (page !== "申报查询") {
						console.log("page======", page)
						this.pbottleRPA.sleep(3000)
						let page1 = this.untils.getCurrentWindowUrl()
						console.log("page1======", page1)
						if (page1 === "错误页面" || page1 === "无标题") {
							console.log("网络异常")
							this.pbottleRPA.keyTap('Ctrl + F4');
							this.pbottleRPA.sleep(500)
							this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
							break;
						} else {
							this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
							break;
						}
					} else {
						console.log("当前内容下载结束")
						this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
						this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
						this.pbottleRPA.browserCMD_remove(`table.commontb tr:contains(增值税):first`)
						break;
					}
				}
				this.pbottleRPA.moveAndClick(position.x, position.y)
				console.log("下载第" + index + "份" + "第" + index2 + "个")

				// 以此为基点，继续下载下一个文件
				fromY = position.y
			}

		}

		let url = await this.untils.uploadFile(1)
		return url
	}

	/**
	 * 企业所得税
	 */
	async process_taxIncome() {
		console.log("开始下载所得税")
		for (let index = 0; index < 99; index++) {
			this.pbottleRPA.browserCMD_click(`table.commontb tr:contains(企业所得税):first input[value="查看"]:first`)
			this.pbottleRPA.sleep(1000)
			let xiangqing = this.pbottleRPA.findScreen("/input/1920/xiangqing.png")

			if (xiangqing === false) {
				break;
			}

			let fromY = xiangqing.y + 30;
			for (let index2 = 0; index2 < 99; index2++) {
				this.pbottleRPA.sleep(500)
				let position = this.pbottleRPA.findScreen("/input/1920/xiazai.png", 0.8, 1000, fromY, 920, 40)
				if (position === false) {
					let page = this.untils.getCurrentWindowUrl()
					if (page !== "申报查询") {
						console.log("page======", page)
						this.pbottleRPA.sleep(3000)
						let page1 = this.untils.getCurrentWindowUrl()
						console.log("page1======", page1)
						if (page1 === "错误页面" || page1 === "无标题") {
							console.log("网络异常")
							this.pbottleRPA.keyTap('Ctrl + F4');
							this.pbottleRPA.sleep(500)
							this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
							break;
						} else {
							this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
							break;
						}
					} else {
						console.log("当前内容下载结束")
						this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
						this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
						this.pbottleRPA.browserCMD_remove(`table.commontb tr:contains(企业所得税):first`)
						break;
					}
				}
				this.pbottleRPA.moveAndClick(position.x, position.y)
				console.log("下载第" + index + "份" + "第" + index2 + "个")

				// 以此为基点，继续下载下一个文件
				fromY = position.y
			}
		}

		let url = await this.untils.uploadFile(4)
		return url
	}

	/**
	 * 财务报表
	 */
	async process_financial() {
		console.log("开始下载财务报表")
		for (let index = 0; index < 99; index++) {
			this.pbottleRPA.browserCMD_click(`table.commontb tr:contains(财务报表):first input[value="查看"]:first`)
			this.pbottleRPA.sleep(1000)

			let xiangqing = this.pbottleRPA.findScreen("/input/1920/xiangqing.png")
			if (xiangqing === false) {
				break;
			}

			let fromY = xiangqing.y + 30;
			for (let index2 = 0; index2 < 99; index2++) {
				this.pbottleRPA.sleep(500)
				let position = this.pbottleRPA.findScreen("/input/1920/xiazai.png", 0.8, 1000, fromY, 920, 40)
				if (position === false) {
					let page = this.untils.getCurrentWindowUrl()
					if (page !== "申报查询") {
						console.log("page======", page)
						this.pbottleRPA.sleep(3000)
						let page1 = this.untils.getCurrentWindowUrl()
						console.log("page1======", page1)
						if (page1 === "错误页面" || page1 === "无标题") {
							console.log("网络异常")
							this.pbottleRPA.keyTap('Ctrl + F4');
							this.pbottleRPA.sleep(500)
							this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
							break;
						} else {
							this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
							break;
						}
					} else {
						console.log("当前内容下载结束")
						this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
						this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
						this.pbottleRPA.browserCMD_remove(`table.commontb tr:contains(财务报表):first`)
						break;
					}
				}
				this.pbottleRPA.moveAndClick(position.x, position.y)
				console.log("下载第" + index + "份" + "第" + index2 + "个")

				// 以此为基点，继续下载下一个文件
				fromY = position.y
			}

		}

		let url = await this.untils.uploadFile(2)
		return url
	}


	/**
	 * 征收信息
	 */
	async process_taxLevy() {
		console.log("开始下载征收信息")
		this.pbottleRPA.openURL('https://etax.jiangsu.chinatax.gov.cn/portal/index.do')
		this.pbottleRPA.sleep(2000)

		this.pbottleRPA.browserCMD_click(`li:contains(我要查询)`)
		this.pbottleRPA.browserCMD_click(`div p:contains(缴款信息查询)`)

		let rs = this.untils.waitImage("/input/1920/jiaoknd.png")
		this.pbottleRPA.moveAndClick(rs.x, rs.y)

		for (let index = 0; index < 4; index++) {

			let year = php.date('Y', php.strtotime(`-${index} year`))
			this.pbottleRPA.browserCMD_val('input[name="search_rknd"]', year)

			this.pbottleRPA.browserCMD_click(`input[value="查 询"]:first`)

			let html = this.pbottleRPA.browserCMD_html('div.datagrid-body:last')
			this.untils.getFs().writeFileSync(global_download_path + `/${year}.html`, html)
		}

		let url = await this.untils.uploadFile(3)

		//清理窗口
		this.pbottleRPA.keyTap('ctrl+w')
		return url

	}

	/**
	 * 纳税信用评价等级
	 */
	async process_taxRatLevle() {
		console.log("开始下载纳税信用评价等级")
		this.pbottleRPA.openURL('https://etax.jiangsu.chinatax.gov.cn/portal/include.do?app_bh=04019')
		// this.pbottleRPA.browserCMD_click(`li:contains(我要查询)`)
		// this.pbottleRPA.browserCMD_click(`div p:contains(纳税信用评价结果查询)`)

		let rs = this.pbottleRPA.findScreen("/input/1920/pingjiajieguo.png")
		if (rs === false) {
			this.pbottleRPA.keyTap('F5')
			this.pbottleRPA.sleep(3 * 1000)
		}
		var currentDate = new Date();
		const data = []
		for (let index = 0; index < 3; index++) {
			var year = currentDate.getFullYear()
			console.log("-----", year)
			this.pbottleRPA.browserCMD_val('[id="search_pdnd"]', year)
			this.pbottleRPA.browserCMD_click('[id="search_bt"]')
			let result = this.pbottleRPA.browserCMD_text('tbody[id="result"]')
			console.log(result, result.substring(4, 6), result.substring(6));
			currentDate.setFullYear(currentDate.getFullYear() - 1)
			const newData = {
				time: year,
				level: result.substring(4, 6),
				score: result.substring(6)
			}
			data.push(newData)
		}
		console.log("data+++++", data)

		// 创建一个函数来写入 Excel 文件
		const workbook = new ExcelJS.Workbook();
		const worksheet = workbook.addWorksheet('Sheet1');

		// 写入标题行
		worksheet.addRow(['评价年度', '评价级别', '评价得分']);

		// 遍历数据并写入到工作表中
		data.forEach(({
			time,
			level,
			score
		}) => {
			worksheet.addRow([time, level, score]);
		});

		// 生成唯一的文件名
		const filename = `纳税信用等级评价.xlsx`;
		const filePath = path.join(global_download_path, filename);

		// 保存工作簿到文件
		workbook.xlsx.writeFile(filePath);
		console.log(`Excel 文件 "${filename}" 已保存到路径 "${filePath}"。`);
		let url = await this.untils.uploadFile(5)
		return url

	}

	async process_submit(url1, url2, url3, url4, url5) {
		// 完成子任务
		let json = this.untils.http('POST', 'api/blade-workflow/app/task/rpaCompleteTask', {
			'taskId': this.childrenTask.taskId,
			"pass": true,
			"variables": {
				"taxFiles": [{
						label: "增值税信息",
						value: url1
					},
					{
						label: "所得税信息",
						value: url2
					},
					{
						label: "征收信息",
						value: url3
					},
					{
						label: "财务报表",
						value: url4
					},
					{
						label: "纳税信用评价等级",
						value: url5
					},
				],
			}

		})
		if (!json.success) {
			this.pbottleRPA.exit('子任务提交失败:' + global_taskName)
		}

	}

}

module.exports = ExtendClass;
