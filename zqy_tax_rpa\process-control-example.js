const { 
    startBothScripts, 
    stopAllProcesses, 
    stopProcess, 
    checkProcessStatus 
} = require('./lufei-download');

// 示例1: 基本使用 - 启动进程，运行一段时间后停止
async function basicExample() {
    console.log('=== 基本使用示例 ===');
    
    try {
        // 启动两个进程
        const processes = await startBothScripts();
        
        // 检查进程状态
        checkProcessStatus(processes);
        
        // 运行10秒
        console.log('进程将运行10秒...');
        await sleep(10000);
        
        // 停止所有进程
        await stopAllProcesses(processes);
        
    } catch (error) {
        console.error('基本示例执行出错:', error);
    }
}

// 示例2: 分别控制进程
async function separateControlExample() {
    console.log('\n=== 分别控制进程示例 ===');
    
    try {
        // 启动两个进程
        const processes = await startBothScripts();
        
        // 运行5秒
        await sleep(5000);
        
        // 只停止 ZIP 进程
        console.log('停止 ZIP 进程...');
        await stopProcess(processes, 'zip');
        
        // 检查状态
        checkProcessStatus(processes);
        
        // 再运行5秒
        await sleep(5000);
        
        // 停止剩余的进程
        console.log('停止剩余进程...');
        await stopProcess(processes, 'upload');
        
    } catch (error) {
        console.error('分别控制示例执行出错:', error);
    }
}

// 示例3: 交互式控制
async function interactiveExample() {
    console.log('\n=== 交互式控制示例 ===');
    
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    let processes = null;
    
    console.log('可用命令:');
    console.log('  start - 启动进程');
    console.log('  stop - 停止所有进程');
    console.log('  stop zip - 停止ZIP进程');
    console.log('  stop upload - 停止UPLOAD进程');
    console.log('  status - 查看进程状态');
    console.log('  quit - 退出');
    
    const handleCommand = async (input) => {
        const parts = input.trim().split(' ');
        const command = parts[0].toLowerCase();
        
        try {
            switch (command) {
                case 'start':
                    if (processes) {
                        console.log('进程已经在运行中');
                    } else {
                        processes = await startBothScripts();
                        console.log('进程已启动');
                    }
                    break;
                    
                case 'stop':
                    if (!processes) {
                        console.log('没有运行的进程');
                    } else if (parts.length > 1) {
                        // 停止指定进程
                        await stopProcess(processes, parts[1]);
                    } else {
                        // 停止所有进程
                        await stopAllProcesses(processes);
                        processes = null;
                    }
                    break;
                    
                case 'status':
                    if (processes) {
                        checkProcessStatus(processes);
                    } else {
                        console.log('没有运行的进程');
                    }
                    break;
                    
                case 'quit':
                case 'exit':
                    if (processes) {
                        console.log('正在停止所有进程...');
                        await stopAllProcesses(processes);
                    }
                    rl.close();
                    return;
                    
                default:
                    console.log('未知命令');
            }
        } catch (error) {
            console.error('执行命令时出错:', error);
        }
        
        // 继续监听下一个命令
        rl.question('请输入命令: ', handleCommand);
    };
    
    // 开始交互
    rl.question('请输入命令: ', handleCommand);
}

// 工具函数: 睡眠
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    const example = args[0] || 'basic';
    
    switch (example) {
        case 'basic':
            await basicExample();
            break;
        case 'separate':
            await separateControlExample();
            break;
        case 'interactive':
            await interactiveExample();
            break;
        default:
            console.log('可用示例:');
            console.log('  node process-control-example.js basic');
            console.log('  node process-control-example.js separate');
            console.log('  node process-control-example.js interactive');
    }
}

// 如果直接运行此文件
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    basicExample,
    separateControlExample,
    interactiveExample
};
