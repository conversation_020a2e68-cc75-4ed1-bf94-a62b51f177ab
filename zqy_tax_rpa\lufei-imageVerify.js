const Untils = require('./untils')
const request = require('sync-request'); //默认同步请求
const pbottleRPA = require('./pbottleRPA')
const xlsx = require("node-xlsx");
const fs = require('fs')
const php = require("php2javascript");
const {
	FormData
} = require('sync-request');
const ExcelJS = require('exceljs');
const path = require('path');
const global_access_token = 'Basic c2FiZXI6c2FiZXJfc2VjcmV0';
const untils = new Untils(global_access_token);

main()
let time = 0
async function main() {
    let res = await getVerifyImage()
    const getCapDetialRes  = untils.getCapDetial(res.slider_block,res.slider_back)
    time++
    // console.log('得到的结果',getCapDetialRes.captcha_solution2)
    console.log('得到的结果',getCapDetialRes.captcha_solution2.target[0])

    check_verifyImage(getCapDetialRes.captcha_solution2.target[0])
}

async function getVerifyImage(){
    await pbottleRPA.browserCMD_click(`button span:contains(登录)`)
    const top  = await pbottleRPA.browserCMD_css(`#slideVerify > div:nth-child(1) > img`,'top') 
    console.log(top)
    let varifyImage = await pbottleRPA.browserCMD_attr('div#slideVerify img', 'src')
    let slider_back = JSON.parse(varifyImage)[0]
    let slider_block = JSON.parse(varifyImage)[1]

    return {slider_back,slider_block}
}

async function check_verifyImage (distance){
    let slider  = await untils.waitImage('/input/1920/slider.png')
    console.log('滑块验证',slider)
    pbottleRPA.moveMouseSmooth(slider.x,slider.y)
    pbottleRPA.mouseLeftDragTo(slider.x + distance + 5,slider.y)
    let slider_check = await untils.existImage2('/input/1920/slider.png')
    console.log('slider_check',slider_check)
    if(slider_check){
        console.log('验证失败')
        pbottleRPA.moveMouseSmooth(200,200)
        pbottleRPA.mouseClick()
        pbottleRPA.sleep(500)
        main()
    }else{
        console.log('验证成功,'+`尝试了${time}次`)
        var loginMesg = await pbottleRPA.browserCMD_text(`body > div.el-message.el-message--warning.el-icon-warning-color > p`)
        if (loginMesg != 'ok') {
            console.log("密码报错", loginMesg)
            
        }
        var loginMesg1 = await pbottleRPA.browserCMD_text(`div[aria-label="提示"] p`)
        if (loginMesg1 != 'ok') {
            console.log("密码报错1", loginMesg1)
        }
    }
}

function checkVerifyImage(){
    let ewm = pbottleRPA.browserCMD_attr('div#qrcodeDivE img', 'src')
    console.log('二维码',ewm)
}