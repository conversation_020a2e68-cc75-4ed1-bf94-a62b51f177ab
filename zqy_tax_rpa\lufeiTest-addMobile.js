const Untils = require('./untils')
const request = require('sync-request'); //默认同步请求
const pbottleRPA = require('./pbottleRPA')
const xlsx = require("node-xlsx");
const fs = require('fs')
const php = require("php2javascript");
const {
	FormData
} = require('sync-request');
const ExcelJS = require('exceljs');
const path = require('path');
const global_access_token = 'Basic c2FiZXI6c2FiZXJfc2VjcmV0';
const untils = new Untils(global_access_token);
const proItem = {
	url: 'jiangsu'
}
const { getAddMobileEWM, getAddMobileEWMResult, enterMobile, getAddMobileResult } = require('./AddMobile.js')

main()

async function main(){
	// let proItem = {
	// 	url: 'jiangsu'
	// }
	// await getAddMobileEWM(proItem)
	// await getAddMobileEWMResult()
	// await enterMobile()
	// await getAddMobileResult()
	let allselect = await untils.waitImage('/input/1920/allselect.png')
	pbottleRPA.moveMouseSmooth(allselect.x, allselect.y)
	pbottleRPA.mouseClick()
}



