const fs = require('fs');
const path = require('path');
const AdmZip = require('adm-zip');
const xlsx = require("node-xlsx");
const workSheetsFromFile = xlsx.parse(`${__dirname}\\配置项.xlsx`);
const config_sheet = workSheetsFromFile[0].data;
// 指定要监听的文件夹路径
const folderPath = config_sheet[1][2];

// 处理 zip 文件的函数，包含重试机制
async function processZipFile(filePath, filename, retryCount = 0) {
    const maxRetries = 3;
    const retryDelay = 2000; // 2秒

    try {
        // 检查文件是否仍然存在
        if (!fs.existsSync(filePath)) {
            console.log(`文件不存在，跳过处理: ${filename}`);
            return;
        }

        // 尝试打开文件以检查是否可以访问
        const fileHandle = await fs.promises.open(filePath, 'r');
        await fileHandle.close();

        // console.log(`开始处理压缩文件: ${filename} (尝试 ${retryCount + 1}/${maxRetries + 1})`);

        // 创建一个新的 AdmZip 实例
        const zip = new AdmZip(filePath);

        // 解压文件到指定的文件夹
        zip.extractAllTo(folderPath, true);
        console.log(`解压完成: ${filename}`);

        // 解压完成后删除压缩文件
        fs.unlinkSync(filePath);
        console.log(`已删除压缩文件: ${filename}`);


    } catch (err) {
        // console.error(`处理压缩文件时出错 (尝试 ${retryCount + 1}): ${err.message}`);

        if (retryCount < maxRetries) {
            // console.log(`${retryDelay/1000}秒后重试...`);
            setTimeout(() => {
                processZipFile(filePath, filename, retryCount + 1);
            }, retryDelay);
        } else {
            console.error(`处理文件失败，已达到最大重试次数: ${filename}`);
        }
    }
}
// 创建一个文件夹监听器
const watcher = fs.watch(folderPath, async (eventType, filename) => {
    if (eventType === 'rename') { // 'rename' 事件在文件创建或删除时触发
        const filePath = path.join(folderPath, filename);
        // 检查文件是否存在（防止误报删除事件）
        if (fs.existsSync(filePath)) {
            // 检查文件是否为压缩文件（这里以 .zip 为例）
            if (path.extname(filename).toLowerCase() === '.zip') {
                console.log(`检测到新的压缩文件: ${filename}`);

                // 等待文件完全写入完成
                setTimeout(async () => {
                    await processZipFile(filePath, filename);
                }, 1000); // 延迟1秒等待文件完全写入
            }
        }
    }
});
console.log(`正在监听文件夹: ${folderPath}`);
// 监听器错误处理
watcher.on('error', (err) => {
    console.error(`监听文件夹时出错: ${err}`);
});