const fs = require('fs');
const path = require('path');
const AdmZip = require('adm-zip');
const xlsx = require("node-xlsx");

// 解析配置文件
const workSheetsFromFile = xlsx.parse(`${__dirname}\\配置项.xlsx`);
const config_sheet = workSheetsFromFile[0].data;

// 指定要监听的文件夹
const folderPath = config_sheet[1][2];

// 处理 zip 文件的函数，包含重试机制
async function processZipFile(filePath, filename, retryCount = 0) {
    const maxRetries = 3;
    const retryDelay = 2000; // 2秒

    try {
        // 检查文件是否仍然存在
        if (!fs.existsSync(filePath)) {
            console.log(`文件不存在，跳过处理: ${filename}`);
            return;
        }

        // 尝试打开文件以检查是否可以访问
        const fileHandle = await fs.promises.open(filePath, 'r');
        await fileHandle.close();

        console.log(`开始处理压缩文件: ${filename} (尝试 ${retryCount + 1}/${maxRetries + 1})`);

        // 创建一个新的 AdmZip 实例
        const zip = new AdmZip(filePath);

        // 解压文件到指定的文件夹
        zip.extractAllTo(folderPath, true);
        console.log(`解压完成: ${filename}`);
        // 假设 untils.addLogForPDF 是一个日志记录函数，这里假设它已经定义
        // untils.addLogForPDF('uploadPDF', `解压完成: ${filename}`, 'uploadPDF')

        // 解压完成后删除压缩文件
        fs.unlinkSync(filePath);
        console.log(`已删除压缩文件: ${filename}`);
    } catch (err) {
        console.error(`处理压缩文件时出错 (尝试 ${retryCount + 1}): ${err.message}`);

        if (retryCount < maxRetries) {
            setTimeout(() => {
                processZipFile(filePath, filename, retryCount + 1);
            }, retryDelay);
        } else {
            console.error(`处理文件失败，已达到最大重试次数: ${filename}`);
        }
    }
}

// 读取文件夹中的所有文件
fs.readdir(folderPath, (err, files) => {
    if (err) {
        console.error(`读取文件夹时出错: ${err}`);
        return;
    }

    // 过滤出所有的 zip 文件
    const zipFiles = files.filter(file => path.extname(file).toLowerCase() === '.zip');

    console.log(`文件夹中有 ${zipFiles.length} 个 zip 文件`);

    // 对每个 zip 文件进行处理
    zipFiles.forEach(filename => {
        const filePath = path.join(folderPath, filename);
        console.log(`检测到新的压缩文件: ${filename}`);
        setTimeout(async () => {
            await processZipFile(filePath, filename);
        }, 1000); // 延迟1秒等待文件完全写入
    });
});
