const Untils = require("./untils");
const untils = new Untils();
const xlsx = require("node-xlsx");
const workSheetsFromFile = xlsx.parse(`${__dirname}\\配置项.xlsx`);
const config_sheet = workSheetsFromFile[0].data;
const folderPath = config_sheet[1][2];
const fs = require('fs');
const path = require('path');
const ossUrl = 'https://zqcloud.shuzutech.com';
const OSS = require('ali-oss');

const client = new OSS({
    endpoint: 'oss-cn-shanghai.aliyuncs.com',
    accessKeyId: 'LTAI5tEZoVaq5zroB7urbauK',
    accessKeySecret: '******************************',
    bucket: 'zhenqi-cloud',
});

// 读取文件夹中的所有文件
fs.readdir(folderPath, async (err, files) => {
    if (err) {
        console.error(`读取文件夹时出错: ${err}`);
        return;
    }

    for (const file of files) {
        const filePath = path.join(folderPath, file);

        // 检查文件是否为PDF文件
        if (path.extname(file).toLowerCase() === '.pdf') {
            console.log(`检测到新的PDF文件: ${file}`);
            // 找到最后一个下划线的位置
            const lastUnderscoreIndex = file.lastIndexOf('_');
            // 找到文件扩展名 .pdf 的位置
            const fileExtensionIndex = file.lastIndexOf('.pdf');
            // 截取最后一个下划线之前的部分，并加上文件扩展名
            const newFilename = file.substring(0, lastUnderscoreIndex) + file.substring(fileExtensionIndex);
            console.log(newFilename);
            // 重命名文件
            const fileName = '/eInvoice/' + newFilename;
            let fileUrl = ossUrl + fileName;
            try {
                await client.put(fileName, filePath);
                console.log('文件上传成功:', fileUrl);
                // untils.addLogForPDF('uploadPDF', '上传PDF文件成功:' + fileUrl, 'uploadPDF');
                // 上传成功后删除文件
                await fs.promises.unlink(filePath);
                console.log('文件已删除:', filePath);
            } catch (error) {
                console.error('文件上传失败:', error);
                // untils.addLogForPDF('uploadPDF', '上传PDF文件失败:' + fileUrl + '失败原因:' + error, 'uploadPDF');
            }
        } else if (path.extname(file).toLowerCase() === '.xlsx') {
            console.log(`检测到xlsx文件: ${file}`);
            // untils.addLogForPDF('uploadPDF', 'xlsx文件:' + file, 'uploadPDF');
            // 删除xlsx文件
            await fs.promises.unlink(filePath);
            console.log('文件已删除:', filePath);
        }
    }
});

console.log(`正在上传文件夹: ${folderPath} 中的PDF文件`);
