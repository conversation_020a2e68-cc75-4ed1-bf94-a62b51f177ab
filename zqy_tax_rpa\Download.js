const Untils = require("./untils");
const untils = new Untils();
const pbottleRPA = require("./pbottleRPA");
const { spawn } = require('child_process');

let getherInvoice = async () => {
    const begin = 1699771103000 
    const end = 1762843103000
    const times = calculateYear(begin,end)
    console.log("进入gather_invoice");
    pbottleRPA.openURL(
        `https://dppt.jiangsu.chinatax.gov.cn:8443/invoice-query/invoice-query/`
    );
    let downloaStartTime = new Date().getTime();
    console.log('downloaStartTime',downloaStartTime)
    var count = 0;
    let chaxuntype = await untils.waitImage("/input/1920/chaxuntype.png");
    if (chaxuntype) {
        pbottleRPA.moveMouseSmooth(chaxuntype.x, chaxuntype.y);
    }
    for (let index = 0; index < 2; index++) {
        if (index == 1) {
        //chaxuntype.png
        pbottleRPA.moveMouseSmooth(chaxuntype.x + 150, chaxuntype.y);
        pbottleRPA.mouseClick(); //focus
        pbottleRPA.browserCMD_click(`li span:contains(取得发票)`);
        }

        const fplx = index === 0 ? "销项" : "进项";
        // 获取采集截至日期
        var currentDate = ''
        if(end){
            currentDate = new Date(end) 
        }else{
            currentDate = new Date();
             // 获取当前日期的上一个月
            currentDate.setMonth(currentDate.getMonth() - 1);
        }

        // 循环输出3年的数据
        for (var i = 1; i <= times; i++) {
        let currentCount = 0;
        if (index == 1 && count != currentCount) {
            //chaxuntype.png
            currentCount = count;
            pbottleRPA.moveMouseSmooth(chaxuntype.x + 150, chaxuntype.y);
            pbottleRPA.mouseClick(); //focus
            pbottleRPA.browserCMD_click(`li span:contains(取得发票)`);
        }
       if (begin && end) {
			if( i == 1 ){
				var startDate = new Date(
					currentDate.getFullYear()-1,
					currentDate.getMonth(),
					currentDate.getDate() + 1 
				)
				var endDate = new Date(end)
			}else{
				var startDate = new Date(
					currentDate.getFullYear(),
					currentDate.getMonth(),
					currentDate.getDate() + 1 
				)
				var endDate = new Date(
					currentDate.getFullYear(),
					currentDate.getMonth() ,
					currentDate.getDate()
				);
			}
			if( i == times ){
				var startDate = new Date(begin)
			}else{
				var endDate = new Date(
					currentDate.getFullYear(),
					currentDate.getMonth() ,
					currentDate.getDate()
				);
			}
	
		}else{
			// 获取11个月前的日期
			var startDate = new Date(
				currentDate.getFullYear(),
				currentDate.getMonth() - 11,
				1
			);
			// 获取当前月份的最后一天
			var endDate = new Date(
				currentDate.getFullYear(),
				currentDate.getMonth() + 1,
				0
			);
		}
		
		// 输出日期范围
		console.log(formatDate(startDate) + "---------->" + formatDate(endDate));

        // 将当前日期往前推12个月
        currentDate.setMonth(currentDate.getMonth() - 12);

        pbottleRPA.browserCMD_click(`input[placeholder="开票日期起"]`);
        pbottleRPA.browserCMD_val(
            'input[placeholder="开票日期起"]',
            formatDate(startDate)
        );
        pbottleRPA.keyTap("Enter");

        pbottleRPA.browserCMD_click(`input[placeholder="开票日期止"]`);
        pbottleRPA.browserCMD_val(
            'input[placeholder="开票日期止"]',
            formatDate(endDate)
        );
        pbottleRPA.keyTap("Enter");

        pbottleRPA.browserCMD_click(`button span:contains(查询)`);
        console.log("-------------查询结束---------------");
        pbottleRPA.sleep(1000);
        let daochu = pbottleRPA.browserCMD_text(
            'div[class="button__export"] > button > span:nth-child(1)'
        );
        if (daochu === "ok") {
            console.log("-------------查询无发票数据--------------");
            console.log(
            `${formatDate(startDate)}` +
                "---" +
                `${formatDate(endDate)}` +
                "年度无" +
                `${fplx}` +
                "数据"
            );
            untils.addLog(
            global.traceId,
            `${formatDate(startDate)}` +
                "---" +
                `${formatDate(endDate)}` +
                "年度无" +
                `${fplx}` +
                "数据",
            "gather_invoice"
            );
        } else {
            pbottleRPA.sleep(500);
            let text = pbottleRPA.browserCMD_text(
            `div[class="t-pagination__total"]`
            ); //div[class = "statistics-info"] span:nth-child(1)
            let match = text.match(/\d+/);
            let number = match ? parseInt(match[0]) : 0;
            console.log("===============", text, number);
            pbottleRPA.browserCMD_click(`button span:contains(导出 )`);
            pbottleRPA.browserCMD_click(`li span:contains(导出全部)`);
            let downloading = await untils.existImage("/input/1920/dowloading.png");
            // let wxts = untils.existImage("/input/1920/wxts.png")
            // let wxts = untils.waitImage("/input/1920/wxts.png")
            if (downloading) {
            console.log("-------------直接开始下载---------------");
            } else if (!downloading) {
            let submissionTimes = [];
            count++;
            // 发票数量大于2500，异步下载
            console.log("-------------提交异步下载---------------");
            if (!downloading) {
                console.log("温馨提示");
                let wxts1 = await untils.waitImage("/input/1920/wxts.png");
                pbottleRPA.sleep(500);
                pbottleRPA.moveMouseSmooth(wxts1.x + 20, wxts1.y + 180);
                pbottleRPA.mouseClick(); //focus
            }
            untils.addLog(
                global.traceId,
                `${formatDate(startDate)}` +
                "---" +
                `${formatDate(endDate)}` +
                `${fplx}` +
                "数据提交异步下载",
                "gather_invoice"
            );
            pbottleRPA.sleep("30000");
            pbottleRPA.keyTap("alt + Left");
            }
            let down1 = await untils.waitImageDisappear(
            "/input/1920/dowloading.png"
            );
            if (down1) {
            console.log("-------------已下载---------------");
            untils.addLog(
                global.traceId,
                `${formatDate(startDate)}` +
                "---" +
                `${formatDate(endDate)}` +
                `${fplx}` +
                "数据下载完成",
                "gather_invoice"
            );
            pbottleRPA.sleep(2000);
            }
        }
        }
    }
    console.log("count+++++++++++++++++++++++++++++", count)
    if (count > 0) {
        downloadEXCEL(count,downloaStartTime,'卜宁川','jiangsu')
    }

    function exportFPData(){
        
    }
};

let downloadEXCEL = async (count,startTime,xm,province) => {
    pbottleRPA.moveMouseSmooth(1000,300)
    pbottleRPA.mouseClick()
    pbottleRPA.openURL(`https://dppt.${province}.chinatax.gov.cn:8443/importing-exporting-batches-search`)
    const chaxun = await untils.existImage('/input/1920/chaxun.png')
    if(chaxun){
        // 选择任务状态
        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.search_header > form > div > div > div:nth-child(1) > div > div.t-form__controls > div > div > div > div > div')
        pbottleRPA.browserCMD_click('body > div:nth-child(8) > div > div > div > ul.t-select__list.t-all-list > li > label')
        pbottleRPA.browserCMD_click('body > div:nth-child(8) > div > div > div > ul:nth-child(2) > li:nth-child(3)')
        // 选择当前日期
        const currentDate = new Date()
        pbottleRPA.browserCMD_click(`input[placeholder="请选择"]`)
        pbottleRPA.browserCMD_val('input[placeholder="请选择"]', formatDate(currentDate))
        pbottleRPA.keyTap('Enter')
        // 选择任务所属功能
        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.search_header > form > div > div > div:nth-child(4) > div > div.t-form__controls > div > div > div > div > div')
        pbottleRPA.browserCMD_click('body > div:nth-child(10) > div > div.t-popup__content > div > ul.t-select__list.t-all-list > li > label')
        pbottleRPA.browserCMD_click('body > div:nth-child(10) > div > div > div > ul:nth-child(2) > li:nth-child(4)')
        pbottleRPA.mouseClick()
        // 点击查询
        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.t-loading__parent > div.project-table__container > div > div:nth-child(4) > div > div > div > div > div.t-select__wrap.t-pagination__select > div > div')
        pbottleRPA.browserCMD_click('body > div:nth-child(11) > div > div > div > ul > li:nth-child(3)')
        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.search_header > form > div > div > div:nth-child(8) > div > div > div > button.t-button.t-button--variant-base.t-button--theme-primary')
        handleDownloadData(count,startTime,xm,province)
    }else{
        downloadEXCEL(count,startTime,xm,province)
    }
};

const handleDownloadData = async (count,startTime,xm,province) => {
    console.log('当前办税员姓名',xm)
    let downloadData = [];
    let total = pbottleRPA.browserCMD_text("div.t-pagination__total");
    total = Number(total.replace("共", "").replace("条", ""));
    console.log("total", total);
    if (total > 0) {
        for (let n = 1; n <= total; n++) {
        // 当数据条数和异步任务的数量一致时，退出循环开始下载
        if(downloadData.length == count){
            console.log('downloadData',downloadData)
            for(let i = 1; i <= downloadData.length; i++){
                // console.log(`开始下载第${i}个文件`)
                pbottleRPA.browserCMD_click(
                    `div[class="t-table__content"] tr:nth-child(${downloadData[i-1].n}) span[class="operate"]`
                )
                console.log(`第${i}个文件下载完成`)
                untils.addLog(global.traceId,`批量任务共${downloadData.length}个文件待下载,第${i}个文件下载完成`,"gather_invoice")
            }
            console.log('全部下载完成')
            untils.addLog(global.traceId,`批量任务共${downloadData.length}个文件全部下载完成`,"gather_invoice")
            break;
        }  
        const id = pbottleRPA
            .browserCMD_text(
            `div[class="t-table__content"] tbody tr:nth-child(${n}) td:nth-child(1)`
            )
            .trim();
        const submitter = pbottleRPA
            .browserCMD_text(
            `div[class="t-table__content"] tbody tr:nth-child(${n}) td:nth-child(3)`
            )
            .trim();
        const submitTime = pbottleRPA
            .browserCMD_text(
            `div[class="t-table__content"] tbody tr:nth-child(${n}) td:nth-child(5)`
            )
            .trim();
        const status = pbottleRPA
            .browserCMD_text(
            `div[class="t-table__content"] tbody tr:nth-child(${n}) td:nth-child(7)`
            )
            .trim();
        const downloadButton = pbottleRPA
            .browserCMD_text(
            `div[class="t-table__content"] tbody tr:nth-child(${n}) td:nth-child(11)`
            )
            .trim();
        const submitTimeISO = submitTime.replace(" ", "T"); // 将空格替换为 'T'
        const submitTimeTimestamp = new Date(submitTimeISO).getTime();
        console.log(
            "00000000000",
            id,
            submitter,
            submitTime,
            submitTimeTimestamp,
            status,
            downloadButton
        );
        console.log('startTime submitTimeTimestamp',startTime,submitTimeTimestamp)
        if (startTime < submitTimeTimestamp ) {
            if(submitter == xm){
                console.log('本人记录')
                downloadData.push({
                    id,
                    submitter,
                    submitTime,
                    submitTimeTimestamp,
                    status,
                    downloadButton,
                    n
                })
            }else{
                console.log('非本人记录')
            }
        } else {
            console.log("数据已过期");
            refresh()
            break;
        }}
    } else {
        console.log("无数据");
        refresh()
    }

    function refresh(){
        pbottleRPA.browserCMD_click(
            "body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.search_header > form > div > div > div:nth-child(8) > div > div > div > button.t-button.t-button--variant-base.t-button--theme-primary"
        );
        handleDownloadData(count,startTime,xm,province);
    }
};

const getPDF = async (childrenTask) => {
    untils.addLog(global.traceId,`开始下载PDF`,"download_PDF")
    pbottleRPA.openURL(
        `https://dppt.jiangsu.chinatax.gov.cn:8443/invoice-query/invoice-query/`
    );
    let chaxuntype = await untils.waitImage("/input/1920/chaxuntype.png")
    if (chaxuntype) {
        pbottleRPA.moveMouseSmooth(chaxuntype.x, chaxuntype.y);
    }
    let count = 0
    pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.t-row.t-row--start.t-row--align-top.search__box > div > form > div > div > div > div:nth-child(4) > div > div.t-form__controls > div > div > div > div > div')
    pbottleRPA.browserCMD_click('body > div:nth-child(19) > div > div > div > ul:nth-child(2) > li:nth-child(1)')
    pbottleRPA.browserCMD_click('body > div:nth-child(19) > div > div > div > ul:nth-child(2) > li:nth-child(2)')
    pbottleRPA.browserCMD_click('body > div:nth-child(19) > div > div > div > ul:nth-child(2) > li:nth-child(3)')
    pbottleRPA.browserCMD_click('body > div:nth-child(19) > div > div > div > ul:nth-child(2) > li:nth-child(4)')
    pbottleRPA.browserCMD_click('body > div:nth-child(19) > div > div > div > ul:nth-child(2) > li:nth-child(17)')
    pbottleRPA.browserCMD_click('body > div:nth-child(19) > div > div > div > ul:nth-child(2) > li:nth-child(18)')
    pbottleRPA.browserCMD_click('body > div:nth-child(19) > div > div > div > ul:nth-child(2) > li:nth-child(19)')
    pbottleRPA.browserCMD_click('div.t-select__wrap.t-pagination__select > div > div > div')
    pbottleRPA.browserCMD_click('body > div:nth-child(20) > div > div > div > ul > li:nth-child(4)')
    pbottleRPA.mouseClick()
    for (let index = 0; index < 2; index++) {
        if (index == 1) {
        //chaxuntype.png
        pbottleRPA.moveMouseSmooth(chaxuntype.x + 150, chaxuntype.y);
        pbottleRPA.mouseClick(); //focus
        pbottleRPA.browserCMD_click(`li span:contains(取得发票)`);
        }

        const fplx = index === 0 ? "销项" : "进项";
        // 获取当前日期
        var currentDate = new Date();

        // 获取当前日期的上一个月
        currentDate.setMonth(currentDate.getMonth() - 1);
        // 循环输出3年的数据
        for (var i = 0; i < 3; i++) {
        
        // 获取11个月前的日期
        var startDate = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth() - 11,
            1
        );

        // 获取当前月份的最后一天
        var endDate = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth() + 1,
            0
        );

        // 输出日期范围
        console.log(formatDate(startDate) + "---------->" + formatDate(endDate));

        // 将当前日期往前推12个月
        currentDate.setMonth(currentDate.getMonth() - 12);

        pbottleRPA.browserCMD_click(`input[placeholder="开票日期起"]`);
        pbottleRPA.browserCMD_val(
            'input[placeholder="开票日期起"]',
            formatDate(startDate)
        );
        pbottleRPA.keyTap("Enter");

        pbottleRPA.browserCMD_click(`input[placeholder="开票日期止"]`);
        pbottleRPA.browserCMD_val(
            'input[placeholder="开票日期止"]',
            formatDate(endDate)
        );
        pbottleRPA.keyTap("Enter");

        pbottleRPA.browserCMD_click(`button span:contains(查询)`);
        console.log("-------------查询结束---------------");
        pbottleRPA.sleep(1000);
        let daochu = pbottleRPA.browserCMD_text(
            'div[class="button__export"] > button > span:nth-child(1)'
        );
        if (daochu === "ok") {
            console.log("-------------查询无发票数据--------------");
            console.log(
            `${formatDate(startDate)}` +
                "---" +
                `${formatDate(endDate)}` +
                "年度无" +
                `${fplx}` +
                "数据"
            );
            untils.addLog(
            global.traceId,
            `${formatDate(startDate)}` +
                "---" +
                `${formatDate(endDate)}` +
                "年度无" +
                `${fplx}` +
                "数据",
            "download_PDF"
            );
        } else {
            pbottleRPA.sleep(500);
            let text = pbottleRPA.browserCMD_text(
            `div[class="t-pagination__total"]`
            ); //div[class = "statistics-info"] span:nth-child(1)
            let match = text.match(/\d+/);
            let number = match ? parseInt(match[0]) : 0;
            console.log("===============", text, number);
            for(let n = 0; n < Math.ceil(number/100); n++){
                pbottleRPA.sleep(1000);
                pbottleRPA.browserCMD_click('div.t-table__content > table > thead > tr > th > div > label > span')
                pbottleRPA.browserCMD_click(`button span:contains(下载)`);
                pbottleRPA.browserCMD_click(`li span:contains(下载选中)`);
                let wjxzlx = await untils.existImage("/input/1920/wjxzlx.png")
                if(wjxzlx){
                    if(n == 0 && i == 0 && index == 0){
                        pbottleRPA.browserCMD_click('div.t-dialog__body.t-dialog__body__icon > div > form > div > div > div > div > label:nth-child(2) > span.t-checkbox__input')
                    }
                    pbottleRPA.browserCMD_click('div.t-dialog__footer > button:nth-child(2)')
                    let dccg = await untils.existImage("/input/1920/dccg.png")
                    if(dccg){
                        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div:nth-child(8) > div > div.t-dialog__wrap > div > div > div.t-dialog__footer > div > button') 
                        count++
                        console.log(`${formatDate(startDate)}` +"---" + `${formatDate(endDate)}` + "年度" +`${fplx}` +"数据," +`第${n+1}页数据下载成功`);
                        untils.addLog(global.traceId,`${formatDate(startDate)}` +"---" + `${formatDate(endDate)}` + "年度" +`${fplx}` +"数据," +`第${n+1}页数据下载成功`,"download_PDF")
                    }else{
                        console.log(`${formatDate(startDate)}` +"---" + `${formatDate(endDate)}` + "年度" +`${fplx}` +"数据," +`第${n+1}页数据下载失败`);
                        untils.addLog(global.traceId,`${formatDate(startDate)}` +"---" + `${formatDate(endDate)}` + "年度" +`${fplx}` +"数据," +`第${n+1}页数据下载失败`,"download_PDF")
                    }
                }else{
                    console.log(`${formatDate(startDate)}` +"---" + `${formatDate(endDate)}` + "年度" +`${fplx}` +"数据," +`第${n+1}页数据下载失败`);
                    untils.addLog(global.traceId,`${formatDate(startDate)}` +"---" + `${formatDate(endDate)}` + "年度" +`${fplx}` +"数据," +`第${n+1}页数据下载失败`,"download_PDF")
                }
                await pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div:nth-child(4) > div:nth-child(2) > div > div > div:nth-child(4) > div.t-affix > div > div > div > div.t-pagination__btn.t-pagination__btn-next')
            }
        }
    }}
    downloadPDF(count)
}
    
const downloadPDF = async (count) => {
    // 同时启动 zip.js 和 uploadPDF.js
    const processes = await startBothScripts();
    pbottleRPA.moveMouseSmooth(1000,300)
    pbottleRPA.mouseClick()
    const province = 'jiangsu'
    pbottleRPA.openURL(`https://dppt.${province}.chinatax.gov.cn:8443/importing-exporting-batches-search`)
    const chaxun = await untils.existImage('/input/1920/chaxun.png')
    if(chaxun){
        // 选择任务状态
        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.search_header > form > div > div > div:nth-child(1) > div > div.t-form__controls > div > div > div > div > div')
        pbottleRPA.browserCMD_click('body > div:nth-child(8) > div > div > div > ul.t-select__list.t-all-list > li > label')
        pbottleRPA.browserCMD_click('body > div:nth-child(8) > div > div > div > ul:nth-child(2) > li:nth-child(3)')
        // 选择当前日期
        const currentDate = new Date()
        pbottleRPA.browserCMD_click(`input[placeholder="请选择"]`)
        pbottleRPA.browserCMD_val('input[placeholder="请选择"]', formatDate(currentDate))
        pbottleRPA.keyTap('Enter')
        // 选择任务所属功能
        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.search_header > form > div > div > div:nth-child(4) > div > div.t-form__controls > div > div > div > div > div')
        pbottleRPA.browserCMD_click('body > div:nth-child(10) > div > div.t-popup__content > div > ul.t-select__list.t-all-list > li > label')
        pbottleRPA.browserCMD_click('body > div:nth-child(10) > div > div > div > ul:nth-child(2) > li:nth-child(5)')
        pbottleRPA.mouseClick()
        // 点击查询
        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.t-loading__parent > div.project-table__container > div > div:nth-child(4) > div > div > div > div > div.t-select__wrap.t-pagination__select > div > div')
        pbottleRPA.browserCMD_click('body > div:nth-child(11) > div > div > div > ul > li:nth-child(3)')
        pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.search_header > form > div > div > div:nth-child(8) > div > div > div > button.t-button.t-button--variant-base.t-button--theme-primary')
        handleDownloadPDF(count)
        pbottleRPA.sleep(60000)
        // 关闭两个进程
        await stopAllProcesses(processes);
    }else{
        downloadPDF(count)
    }
}

const handleDownloadPDF = async (count) => {
    let total = pbottleRPA.browserCMD_text("div.t-pagination__total");
    total = Number(total.replace("共", "").replace("条", ""));
    console.log("total", total);
    console.log("count", count);
    untils.addLog(global.traceId, `共${total}条数据，下载${count}条数据`, "download_PDF");
    if(total >= count){ 
        
        for(let i = 1; i <=  Math.ceil(count/50); i++){
            if(i == Math.ceil(count/50) && count%50 != 0){
                for(let j = 1; j <= count%50; j++){
                    pbottleRPA.browserCMD_click(`div[class="t-table__content"] tr:nth-child(${j}) span[class="operate"]`)
                    console.log(`正在下载第${50*(i-1)+j}个文件`);
                }
            }else{
                for(let j = 1; j <= 50; j++){
                    pbottleRPA.browserCMD_click(`div[class="t-table__content"] tr:nth-child(${j}) span[class="operate"]`)
                    console.log(`正在下载第${50*(i-1)+j}个文件`);
                }
                pbottleRPA.browserCMD_click('div.t-affix > div > div > div > div.t-pagination__btn.t-pagination__btn-next')
            }
        }
    }else{
        refresh()
    }

    function refresh(){
        pbottleRPA.browserCMD_click(
            "body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.search_header > form > div > div > div:nth-child(8) > div > div > div > button.t-button.t-button--variant-base.t-button--theme-primary"
        );
        handleDownloadPDF(count);
    }
}

const calculateYear = (startTime, endTime) => {
    if(startTime,endTime){
         // 将时间戳转换为Date对象
        const startDate = new Date(startTime);
        const endDate = new Date(endTime);
        // 获取年份
        const starYear = startDate.getFullYear();
        const endYear = endDate.getFullYear();
        // 计算年份差
        let yearDifference = endYear - starYear;
        // 检查是否跨年但不足一年的情况
        if (endDate.getMonth() < startDate.getMonth() || (endDate.getMonth() === startDate.getMonth() && endDate.getDate() < startDate.getDate())) {
            yearDifference--;
        }
        // 年份差加一
        yearDifference++;
        console.log('年份差（加一后）:', yearDifference);
        return yearDifference;
    }else{
        return 3
	}
}

const formatDate = (date) => {
    var year = date.getFullYear();
    var month = padZero(date.getMonth() + 1);
    var day = padZero(date.getDate());
    return year + "-" + month + "-" + day;
}

// 给小于10的数字前面补0
const padZero = (num) => {
    return num < 10 ? "0" + num : "" + num;
}

// 同时执行两个脚本的函数
async function startBothScripts() {
    console.log('正在启动 zip.js 和 uploadPDF.js...');

    // 启动 zip.js 进程
    const zipProcess = spawn('node', ['zip.js'], {
        cwd: __dirname,
        stdio: 'pipe'
    });

    // 启动 uploadPDF.js 进程
    const uploadProcess = spawn('node', ['uploadPDF.js'], {
        cwd: __dirname,
        stdio: 'pipe'
    });

    // 处理 zip.js 的输出
    zipProcess.stdout.on('data', (data) => {
        console.log(`[ZIP] ${data.toString().trim()}`);
    });

    zipProcess.stderr.on('data', (data) => {
        console.error(`[ZIP ERROR] ${data.toString().trim()}`);
    });

    zipProcess.on('close', (code) => {
        console.log(`[ZIP] 进程退出，退出码: ${code}`);
    });

    // 处理 uploadPDF.js 的输出
    uploadProcess.stdout.on('data', (data) => {
        console.log(`[UPLOAD] ${data.toString().trim()}`);
    });

    uploadProcess.stderr.on('data', (data) => {
        console.error(`[UPLOAD ERROR] ${data.toString().trim()}`);
    });

    uploadProcess.on('close', (code) => {
        console.log(`[UPLOAD] 进程退出，退出码: ${code}`);
    });

    // 处理进程错误
    zipProcess.on('error', (err) => {
        console.error(`[ZIP] 启动进程时出错: ${err}`);
    });

    uploadProcess.on('error', (err) => {
        console.error(`[UPLOAD] 启动进程时出错: ${err}`);
    });

    console.log('两个脚本已启动，正在后台运行...');

    // 返回进程对象，以便外部控制
    return {
        zipProcess: zipProcess,
        uploadProcess: uploadProcess
    };
}

// 停止所有进程的函数
async function stopAllProcesses(processes) {
    console.log('正在关闭所有进程...');

    const stopPromises = [];

    // 停止 ZIP 进程
    if (processes.zipProcess) {
        stopPromises.push(stopSingleProcess(processes.zipProcess, 'ZIP'));
    }

    // 停止 UPLOAD 进程
    if (processes.uploadProcess) {
        stopPromises.push(stopSingleProcess(processes.uploadProcess, 'UPLOAD'));
    }

    // 等待所有进程停止
    await Promise.all(stopPromises);
    console.log('所有进程已终止');
}

// 停止单个进程的函数
async function stopSingleProcess(process, processName, timeout = 3000) {
    return new Promise((resolve) => {
        if (!process || process.killed) {
            console.log(`${processName} 进程已经停止`);
            resolve();
            return;
        }

        console.log(`终止 ${processName} 进程...`);

        // 监听进程关闭事件
        const onClose = () => {
            console.log(`${processName} 进程已关闭`);
            resolve();
        };

        process.once('close', onClose);

        // 发送终止信号
        process.kill('SIGTERM');

        // 设置超时，如果进程在指定时间内没有停止，强制杀死
        setTimeout(() => {
            if (!process.killed) {
                console.log(`强制杀死 ${processName} 进程`);
                process.removeListener('close', onClose);
                process.kill('SIGKILL');

                // 再等待一点时间确保进程被杀死
                setTimeout(() => {
                    resolve();
                }, 500);
            }
        }, timeout);
    });
}

module.exports = {
    getherInvoice,
    downloadEXCEL,
    getPDF,
    downloadPDF,
    formatDate,
    padZero,
};
