// let jiangsu = require('./jiangsu.js')
// let jiangsu = require('./jiangsuApi.js')
// let hubei = require('./hubei.js')
// let shanxi = require('./shanxi.js')
// let zhejiang = require('./zhejiang.js')
// let shaanxi = require('./shaanxi.js')
// let neimenggu = require('./neimenggu.js')
// let sichuan = require('./sichuan.js')
// let shanghai = require('./shanghai.js')
// let fujian = require('./fujian.js')
// let guangxi = require('./guangxi.js')
// let hebei = require('./hebei.js')
// let shandong = require('./shandong.js')
// let anhui = require('./anhui.js')
// let guangdong = require('./guangdong.js')
// let qinghai = require('./qinghai.js')
// let hunan = require('./hunan.js')
// let chongqing = require('./chongqing.js')
// let xinjiang = require('./xinjiang.js')
// let shenzhen = require('./shenzhen.js')
// let henan = require('./henan.js')
// let hainan = require('./hainan.js')
// let ningbo = require('./ningbo.js')
// let tianjin = require('./tianjin.js')
// let beijing = require('./beijing.js')
// let heilongjiang = require('./heilongjiang.js')
// let qingdao = require('./qingdao.js')
// let jiangxi = require('./jiangxi.js')
// let xiamen = require('./xiamen.js')
// let liaoning = require('./liaoning.js')
// let gansu = require('./gansu.js')
// let jilin = require('./jilin.js')
// let guizhou = require('./guizhou.js')
let common = require('./common.js')

const proDic = {
	'110000': {
		label: '北京',
		url: 'beijing',
	},
	'120000': {
		label: '天津',
		url: 'tianjin',
	},
	'130000': {
		label: '河北',
		url: 'hebei',
	},
	'140000': {
		label: '山西',
		url: 'shanxi',
	},
	'150000': {
		label: '内蒙古自治区',
		url: 'neimenggu',
	},
	'210000': {
		label: '辽宁',
		url: 'liaoning',
	},
	'220000': {
		label: '吉林',
		url: 'jilin',
	},
	'230000': {
		label: '黑龙江',
		url: 'heilongjiang',
	},
	'310000': {
		label: '上海',
		url: 'shanghai',
	},
	'320000': {
		label: '江苏',
		url: 'jiangsu',
	},
	'330000': {
		label: '浙江',
		url: 'zhejiang',
	},
	'340000': {
		label: '安徽',
		url: 'anhui',
	},
	'350000': {
		label: '福建',
		url: 'fujian',
	},
	'360000': {
		label: '江西',
		url: 'jiangxi',
	},
	'370000': {
		label: '山东',
		url: 'shandong',
	},
	'410000': {
		label: '河南',
		url: 'henan',
	},
	'420000': {
		label: '湖北',
		url: 'hubei',
	},
	'430000': {
		label: '湖南',
		url: 'hunan',
	},
	'440000': {
		label: '广东',
		url: 'guangdong',
	},
	'450000': {
		label: '广西',
		url: 'guangxi',
	},
	'460000': {
		label: '海南',
		url: 'hainan',
	},
	'500000': {
		label: '重庆',
		url: 'chongqing',
	},
	'510000': {
		label: '四川',
		url: 'sichuan',
	},
	'520000': {
		label: '贵州',
		url: 'guizhou',
	},
	'530000': {
		label: '云南',
		url: 'yunnan',
	},
	'540000': {
		label: '西藏',
		url: 'xizang',
	},
	'610000': {
		label: '陕西',
		url: 'shaanxi',
	},
	'620000': {
		label: '甘肃',
		url: 'gansu',
	},
	'630000': {
		label: '青海',
		url: 'qinghai',
	},
	'640000': {
		label: '宁夏',
		url: 'ningxia',
	},
	'650000': {
		label: '新疆',
		url: 'xinjiang',
	},
	'710000': {
		label: '台湾',
		url: 'taiwan',
	},
	'810000': {
		label: '香港',
		url: 'xianggang',
	},
	'820000': {
		label: '澳门',
		url: 'aomen',
	},
	'440300': {
		label: '深圳',
		url: 'shenzhen',
	},
	'210200': {
		label: '大连',
		url: 'dalian',
	},
	'370200': {
		label: '青岛',
		url: 'qingdao',
	},
	'330200': {
		label: '宁波',
		url: 'ningbo',
	},
	'350200': {
		label: '厦门',
		url: 'xiamen',
	},
};


let getProClassAndExtend = async (code, childrenTask, pbottleRPA) => {
	//code 地区编码
	//childrenTask 子任务对象
	let proItem = proDic[code]
	console.log("proItem1", proItem)
	//每个省份对应的数据
	if (proItem.class) {
		//如果有自定义的省份类
		let clas = new proItem.class(proItem, pbottleRPA, childrenTask)
		if (clas[childrenTask.taskKey]) {
			//判断当前节点是自定义
			return await clas[childrenTask.taskKey](childrenTask)
			console.log("childrenTask.taskDefinitionKey111", childrenTask.taskDefinitionKey)
		} else {
			//不是自定义
			console.log("childrenTask.taskDefinitionKey222", childrenTask.taskKey)
			return await new common(proItem, pbottleRPA, childrenTask)[childrenTask.taskKey]()
		}
	} else {
		//通用类
		return await new common(proItem, pbottleRPA, childrenTask)[childrenTask.taskKey]()
	}
}


module.exports = {
    getProClassAndExtend,
};