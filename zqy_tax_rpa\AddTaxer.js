const Untils = require('./untils')
const untils = new Untils()
const pbottleRPA = require('./pbottleRPA')


let addTaxer = async ( proItem, childrenTask ) => {
    let errMsg = 'ok'
    pbottleRPA.sleep(1000)
    pbottleRPA.openURL(`https://tpass.${proItem.url}.chinatax.gov.cn:8443/#/correlationEE/add`)
    const tjbsry = await untils.existImage('/input/1920/tjbsry.png')
    console.log('tjbsry',tjbsry)
    if(!tjbsry){
        // 暂无权限添加办税人员
        console.log('暂无权限添加办税人员')
        untils.terminateTask(
            childrenTask.flowId,
            3,
            childrenTask.variable,
            '暂无权限添加办税人员'
        )
        return
    }
    const locate = await untils.waitImage(`/input/1920/tjbsry.png`)
    pbottleRPA.moveMouseSmooth(locate.x, locate.y)
    pbottleRPA.mouseClick()
    pbottleRPA.keyTap('tab')
    pbottleRPA.paste(JSON.parse(childrenTask.variable).name)
    pbottleRPA.keyTap('tab')
    pbottleRPA.keyTap('enter')
    pbottleRPA.keyTap('down')
    pbottleRPA.keyTap('enter')
    pbottleRPA.keyTap('tab')
    pbottleRPA.paste(JSON.parse(childrenTask.variable).idCard)
    pbottleRPA.keyTap('tab')
    pbottleRPA.keyTap('enter')
    pbottleRPA.keyTap('down')
    pbottleRPA.keyTap('enter')
    errMsg = pbottleRPA.browserCMD_text('body > div.el-message.el-message--warning.el-icon-warning-color > p')
    pbottleRPA.sleep(3000)
    let res = await untils.completeTask(
        childrenTask.id,
        childrenTask.flowId,
        childrenTask.flowKey,
        childrenTask.taskKey,
        childrenTask.variable,
        1,
        `办税员信息填写成功`
    )
    return errMsg
}

let getTwiceQRCode = async (childrenTask, coderr) => {
    let ecewm = false
    let addsuccess = false

    pbottleRPA.browserCMD_click('#app > div > div.content.el-row.el-row--flex > div.main-container.el-col.el-col-19 > section > div > div.search-condition-add > form > div.btns > button.el-button.el-button--primary')
    for(let index = 0; index <= 10; index++){
        if(coderr == 'ok'){
            coderr = pbottleRPA.browserCMD_text('body > div.el-message.el-message--warning.el-icon-warning-color > p')
        }
        const tips = await untils.existImage2('/input/1920/add-taxer-tips.png')
        if(tips){
            pbottleRPA.browserCMD_click('body > div.el-message-box__wrapper > div > div.el-message-box__btns > button.el-button.el-button--default.el-button--small.el-button--primary')
        }
        ecewm = await untils.existImage2('/input/1920/ecewm.png')
        addsuccess = await untils.existImage2('/input/1920/add-success.png')
        console.log('ecewm,addsuccess,coderr',ecewm,addsuccess,coderr)
        if(ecewm || addsuccess || coderr !== 'ok'){
            break
        }
        if(index == 10){
            const res2 = await untils.terminateTask(
            childrenTask.flowId,
            3,
            childrenTask.variable,
            `验证超时，未能获取二维码`
            )
            return
        }	
    }
    if(ecewm){
        // 二次认证
        const qrCode = pbottleRPA.browserCMD_attr('div#authorise_qrcode1 > img','src')
        const res = await untils.completeTask(
            childrenTask.id,
            childrenTask.flowId,
            childrenTask.flowKey,
            childrenTask.taskKey,
            JSON.stringify({qrCode: qrCode}),
            1,
            `上传二维码成功`
        )
        return
    }
    if(coderr !== 'ok'){
        // 点击提交时候的异常
        if(coderr.includes('关联关系已存在')){
            // 已存在关联关系需要完成所有步骤
            const res1 = await untils.completeTask(
                childrenTask.id,
                childrenTask.flowId,
                childrenTask.flowKey,
                childrenTask.taskKey,
                childrenTask.variable,
                1,
                `关联关系已存在`
            )
            untils.addLog(global.traceId, `完成获取二次认证二维码节点，关联关系已存在`, 'add_taxer')
            const res2 = await untils.completeTask(
                res1.data,
                childrenTask.flowId,
                childrenTask.flowKey,
                'get_add_taxer_result',
                JSON.stringify({ ...JSON.parse(childrenTask.variable), status: 1 }),
                1,
                `关联关系已存在`
            )
            untils.addLog(global.traceId, `完成获取扫码结果节点，关联关系已存在`, 'get_twice_qrCode')
            const res3 = await untils.completeTask(
                res2.data,
                childrenTask.flowId,
                childrenTask.flowKey,
                'user_refresh',
                childrenTask.variable,
                1,
                `关联关系已存在`
            )
            untils.addLog(global.traceId, `完成用户刷新二维码节点，关联关系已存在`, 'user_refresh')
            return
        }
        console.log('页面报错',coderr)
        untils.terminateTask(
            childrenTask.flowId,
            3,
            childrenTask.variable,
            coderr
        )
        return
    }
    // 如果添加成功，则直接完成全部节点
    if(addsuccess){
        const res1 = await untils.completeTask(
            childrenTask.id,
            childrenTask.flowId,
            childrenTask.flowKey,
            childrenTask.taskKey,
            childrenTask.variable,
            1,
            `无需二维码，直接添加成功`
        )
        console.log('完成添加办税员节点',res1)
        untils.addLog(global.traceId, `完成获取扫码结果节点，无需二维码，直接添加成功`, 'add_taxer')
        const res2 = await untils.completeTask(
            res1.data,
            childrenTask.flowId,
            childrenTask.flowKey,
            'get_add_taxer_result',
            JSON.stringify({ ...JSON.parse(childrenTask.variable), status: 1 }),
            1,
            `无需二维码，直接添加成功`
        )
        untils.addLog(global.traceId, `完成获取二次认证二维码节点，无需二维码，直接添加成功`, 'get_twice_qrCode')
        const res4 = await untils.completeTask(
            res2.data,
            childrenTask.flowId,
            childrenTask.flowKey,
            'user_refresh',
            childrenTask.variable,
            1,
            `无需二维码，直接添加成功`
        )
        untils.addLog(global.traceId, `完成用户刷新二维码节点，无需二维码，直接添加成功`, 'user_refresh')
        return
    }
}

let getAddTaxerResult = async (childrenTask) => {
    let errMsg = 'ok'
    let addsuccess = false
    for(let index = 0; index <= 300 ; index++){
        const codeConfirm = await untils.existImage2('/input/1920/codeConfirm.png')
        console.log('codeConfirm', codeConfirm)
        // 如果没有，则表示扫码完成，跳出循环
        if(!codeConfirm){
            for(let i = 1 ; i <= 10; i++){
                errMsg = pbottleRPA.browserCMD_text('body > div.el-message.el-message--warning.el-icon-warning-color > p')
                addsuccess = await untils.existImage2('/input/1920/add-success.png')
                const tips = await untils.existImage2('/input/1920/add-taxer-tips.png')
                console.log('tips',tips)
                if(tips){
                    pbottleRPA.browserCMD_click('body > div.el-message-box__wrapper > div > div.el-message-box__btns > button.el-button.el-button--default.el-button--small.el-button--primary')
                }
                console.log('errMsg', errMsg)
                console.log('addsuccess', addsuccess)
                if(errMsg !== '20s超时' && errMsg !== 'ok'){
                    break;
                }
                if(addsuccess){
                    break;
                }
                if(i == 10){
                    console.log('验证超时超时')
                    let res = await untils.completeTask(
                        childrenTask.id,
                        childrenTask.flowId,
                        childrenTask.flowKey,
                        childrenTask.taskKey,
                        JSON.stringify({ ...JSON.parse(childrenTask.variable), status: 0 }),
                        1,
                        `验证码超时，请重试`
                    )
                    return
                }
            }
            break;
        }
        if(index === 300){
            // 完成任务，等待前端刷新,只需要完成当前get_add_taxer_result
            console.log('二维码确认超时')
            let res = await untils.completeTask(
                childrenTask.id,
                childrenTask.flowId,
                childrenTask.flowKey,
                childrenTask.taskKey,
                JSON.stringify({ ...JSON.parse(childrenTask.variable), status: 0 }),
                1,
                `二维码超时，请刷新二维码重新获取`
            )
            return
        }
    }
    if(errMsg !== '20s超时' && errMsg !== 'ok'){
        if(errMsg.includes('关联关系已存在')){
            // 已存在关联关系需要完成所有步骤
            const res1 = await untils.completeTask(
                childrenTask.id,
                childrenTask.flowId,
                childrenTask.flowKey,
                childrenTask.taskKey,
                JSON.stringify({ ...JSON.parse(childrenTask.variable), status: 1 }),
                1,
                `关联关系已存在`
            )
            untils.addLog(global.traceId, `完成获取扫码结果节点，关联关系已存在`, 'add_taxer')
            const res2 = await untils.completeTask(
                res1.data,
                childrenTask.flowId,
                childrenTask.flowKey,
                'user_refresh',
                childrenTask.variable,
                1,
                `关联关系已存在`
            )
            untils.addLog(global.traceId, `完成用户刷新二维码节点，关联关系已存在`, 'get_twice_qrCode')
            return
        }
        // 说明页面有报错，终止任务
        console.log('终止任务', errMsg)
        untils.terminateTask(
            childrenTask.flowId,
            3,
            childrenTask.variable,
            errMsg
        )
        return
    }
    if(addsuccess){
        console.log('添加办税员成功，无需用户刷新')
        let res = await untils.completeTask(
            childrenTask.id,
            childrenTask.flowId,
            childrenTask.flowKey,
            childrenTask.taskKey,
            JSON.stringify({ ...JSON.parse(childrenTask.variable), status: 1 }),
            1,
            `添加办税员成功`
        )
        let res2 = await untils.completeTask(
            res.data,
            childrenTask.flowId,
            childrenTask.flowKey,
            'user_refresh',
            childrenTask.variable,
            1,
            `添加办税员成功`
        )
    }
}

module.exports = {
    addTaxer,
    getTwiceQRCode,
    getAddTaxerResult
};
